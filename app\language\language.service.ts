import axiosInstance from "../api/axiosInstance";
import { 
  Language, 
  LanguageResponse, 
  CreateLanguageRequest, 
  UpdateLanguageRequest, 
  LanguageFilters 
} from "./language.model";

// Default translation structure
const defaultTranslations = {
  header: {
    helpline: "Helpline",
    menu: "Menu"
  },
  home: {
    search: "Search",
    location: "Location"
  },
  search: {
    rating: "Rating",
    hotel: "Hotel"
  },
  detail: {
    facilities: "Facilities",
    propertyTypes: "Property Types"
  },
  preview: {
    overview: "Overview",
    readMore: "Read More"
  },
  confirmation: {
    success: "Success",
    failed: "Failed"
  },
  itinerary: {
    confirmed: "Confirmed",
    pending: "Pending"
  },
  profile: {
    name: "Name",
    email: "Email"
  },
  login: {
    name: "Name",
    password: "Password"
  },
  common: {
    room: "Room",
    child: "Child"
  }
};

// Spanish translations
const spanishTranslations = {
  header: {
    helpline: "Línea de ayuda",
    menu: "Menú"
  },
  home: {
    search: "<PERSON><PERSON>",
    location: "Ubicación"
  },
  search: {
    rating: "Calificación",
    hotel: "Hotel"
  },
  detail: {
    facilities: "Instalaciones",
    propertyTypes: "Tipos de propiedad"
  },
  preview: {
    overview: "Resumen",
    readMore: "Leer más"
  },
  confirmation: {
    success: "Éxito",
    failed: "Fallido"
  },
  itinerary: {
    confirmed: "Confirmado",
    pending: "Pendiente"
  },
  profile: {
    name: "Nombre",
    email: "Correo electrónico"
  },
  login: {
    name: "Nombre",
    password: "Contraseña"
  },
  common: {
    room: "Habitación",
    child: "Niño"
  }
};

// Malayalam translations
const malayalamTranslations = {
  header: {
    helpline: "സഹായ ലൈൻ",
    menu: "മെനു"
  },
  home: {
    search: "തിരയുക",
    location: "സ്ഥലം"
  },
  search: {
    rating: "റേറ്റിംഗ്",
    hotel: "ഹോട്ടൽ"
  },
  detail: {
    facilities: "സൗകര്യങ്ങൾ",
    propertyTypes: "പ്രോപ്പർട്ടി തരങ്ങൾ"
  },
  preview: {
    overview: "അവലോകനം",
    readMore: "കൂടുതൽ വായിക്കുക"
  },
  confirmation: {
    success: "വിജയം",
    failed: "പരാജയപ്പെട്ടു"
  },
  itinerary: {
    confirmed: "സ്ഥിരീകരിച്ചു",
    pending: "തീർപ്പുകൽപ്പിക്കാത്ത"
  },
  profile: {
    name: "പേര്",
    email: "ഇമെയിൽ"
  },
  login: {
    name: "പേര്",
    password: "പാസ്‌വേഡ്"
  },
  common: {
    room: "മുറി",
    child: "കുട്ടി"
  }
};

// Mock data generator for development/testing
const generateMockLanguages = (): Language[] => {
  return [
    {
      ID: 4,
      CreatedAt: "2025-10-06T17:22:40.582077+05:30",
      UpdatedAt: "2025-10-06T17:24:37.498146+05:30",
      DeletedAt: null,
      name: "Malayalam",
      country_code: "IN",
      language_code: "mal",
      json: {
        native_name: "മലയാളം",
        region: "South India",
        ...malayalamTranslations
      },
      is_active: true
    },
    {
      ID: 6,
      CreatedAt: "2025-10-07T17:10:30.02598+05:30",
      UpdatedAt: "2025-10-07T17:10:30.02598+05:30",
      DeletedAt: null,
      name: "English",
      country_code: "US",
      language_code: "en",
      json: {
        native_name: "English",
        region: "North America",
        ...defaultTranslations
      },
      is_active: true
    },
    {
      ID: 7,
      CreatedAt: "2025-10-05T14:15:20.123456+05:30",
      UpdatedAt: "2025-10-05T14:15:20.123456+05:30",
      DeletedAt: null,
      name: "Spanish",
      country_code: "ES",
      language_code: "es",
      json: {
        native_name: "Español",
        region: "Europe",
        ...spanishTranslations
      },
      is_active: true
    },
    {
      ID: 8,
      CreatedAt: "2025-10-04T09:30:15.789012+05:30",
      UpdatedAt: "2025-10-04T09:30:15.789012+05:30",
      DeletedAt: null,
      name: "French",
      country_code: "FR",
      language_code: "fr",
      json: {
        native_name: "Français",
        region: "Europe"
      },
      is_active: true
    },
    {
      ID: 9,
      CreatedAt: "2025-10-03T16:45:30.345678+05:30",
      UpdatedAt: "2025-10-03T16:45:30.345678+05:30",
      DeletedAt: null,
      name: "German",
      country_code: "DE",
      language_code: "de",
      json: {
        native_name: "Deutsch",
        region: "Europe"
      },
      is_active: false
    },
    {
      ID: 10,
      CreatedAt: "2025-10-02T11:20:45.567890+05:30",
      UpdatedAt: "2025-10-02T11:20:45.567890+05:30",
      DeletedAt: null,
      name: "Japanese",
      country_code: "JP",
      language_code: "ja",
      json: {
        native_name: "日本語",
        region: "East Asia"
      },
      is_active: true
    }
  ];
};

let mockLanguages: Language[] = generateMockLanguages();

// Language Service API functions
const LanguageService = {
  // Get all languages with pagination and filters
  getAllLanguages: async (
    page: number = 1,
    pageSize: number = 10,
    filters?: LanguageFilters
  ): Promise<LanguageResponse> => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      let filteredLanguages = [...mockLanguages];

      // Apply filters
      if (filters) {
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filteredLanguages = filteredLanguages.filter(lang =>
            lang.name.toLowerCase().includes(searchLower) ||
            lang.language_code.toLowerCase().includes(searchLower) ||
            lang.country_code.toLowerCase().includes(searchLower)
          );
        }

        if (filters.country_code) {
          filteredLanguages = filteredLanguages.filter(lang =>
            lang.country_code === filters.country_code
          );
        }

        if (filters.language_code) {
          filteredLanguages = filteredLanguages.filter(lang =>
            lang.language_code === filters.language_code
          );
        }

        if (filters.is_active !== undefined) {
          filteredLanguages = filteredLanguages.filter(lang =>
            lang.is_active === filters.is_active
          );
        }
      }

      // Apply pagination
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedLanguages = filteredLanguages.slice(startIndex, endIndex);

      return {
        success: true,
        data: paginatedLanguages,
        total: filteredLanguages.length
      };

    } catch (error) {
      console.error('Error fetching languages:', error);
      // Return mock data as fallback
      return {
        success: true,
        data: mockLanguages.slice(0, pageSize),
        total: mockLanguages.length
      };
    }
  },

  // Get language by ID
  getLanguageById: async (id: number): Promise<Language | null> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 300));
      const language = mockLanguages.find(lang => lang.ID === id);
      return language || null;
    } catch (error) {
      console.error('Error fetching language by ID:', error);
      return null;
    }
  },

  // Create new language
  createLanguage: async (languageData: CreateLanguageRequest): Promise<Language> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const newLanguage: Language = {
        ID: Math.max(...mockLanguages.map(l => l.ID)) + 1,
        CreatedAt: new Date().toISOString(),
        UpdatedAt: new Date().toISOString(),
        DeletedAt: null,
        ...languageData,
        json: languageData.json || {}
      };

      mockLanguages.push(newLanguage);
      return newLanguage;
    } catch (error) {
      console.error('Error creating language:', error);
      throw new Error('Failed to create language');
    }
  },

  // Update language
  updateLanguage: async (id: number, languageData: UpdateLanguageRequest): Promise<Language> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const index = mockLanguages.findIndex(lang => lang.ID === id);
      if (index === -1) {
        throw new Error('Language not found');
      }

      const updatedLanguage: Language = {
        ...mockLanguages[index],
        ...languageData,
        UpdatedAt: new Date().toISOString()
      };

      mockLanguages[index] = updatedLanguage;
      return updatedLanguage;
    } catch (error) {
      console.error('Error updating language:', error);
      throw new Error('Failed to update language');
    }
  },

  // Delete language
  deleteLanguage: async (id: number): Promise<boolean> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const index = mockLanguages.findIndex(lang => lang.ID === id);
      if (index === -1) {
        throw new Error('Language not found');
      }

      mockLanguages.splice(index, 1);
      return true;
    } catch (error) {
      console.error('Error deleting language:', error);
      throw new Error('Failed to delete language');
    }
  },

  // Toggle language status
  toggleLanguageStatus: async (id: number): Promise<Language> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 400));

      const index = mockLanguages.findIndex(lang => lang.ID === id);
      if (index === -1) {
        throw new Error('Language not found');
      }

      mockLanguages[index] = {
        ...mockLanguages[index],
        is_active: !mockLanguages[index].is_active,
        UpdatedAt: new Date().toISOString()
      };

      return mockLanguages[index];
    } catch (error) {
      console.error('Error toggling language status:', error);
      throw new Error('Failed to toggle language status');
    }
  },

  // Update language translations only
  updateLanguageTranslations: async (
    id: number,
    translations: Record<string, any>
  ): Promise<Language> => {
    try {
      await new Promise(resolve => setTimeout(resolve, 600));

      const index = mockLanguages.findIndex(lang => lang.ID === id);
      if (index === -1) {
        throw new Error('Language not found');
      }

      // Merge translations with existing json data (preserving native_name, region, etc.)
      const updatedJson = {
        ...mockLanguages[index].json,
        ...translations
      };

      mockLanguages[index] = {
        ...mockLanguages[index],
        json: updatedJson,
        UpdatedAt: new Date().toISOString()
      };

      return mockLanguages[index];
    } catch (error) {
      console.error('Error updating language translations:', error);
      throw new Error('Failed to update language translations');
    }
  }
};

// Export individual functions for easier use
export const {
  getAllLanguages,
  getLanguageById,
  createLanguage,
  updateLanguage,
  deleteLanguage,
  toggleLanguageStatus,
  updateLanguageTranslations
} = LanguageService;

export default LanguageService;
