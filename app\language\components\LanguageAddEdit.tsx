/** @jsxImportSource react */
'use client';

import React, { useState, useEffect } from 'react';
import { Language, CreateLanguageRequest, UpdateLanguageRequest, CommonCountryCodes, CommonLanguageCodes, LanguageValidation } from '../language.model';
import TranslationEditor from './TranslationEditor';
import ExcelTranslationManager from './ExcelTranslationManager';
import SectionWiseManager from './SectionWiseManager';

interface LanguageAddEditProps {
  language?: Language | null;
  onSave: (languageData: CreateLanguageRequest | UpdateLanguageRequest) => void;
  onCancel: () => void;
  mode?: 'add' | 'edit';
}

interface FormErrors {
  name?: string;
  country_code?: string;
  language_code?: string;
  native_name?: string;
  region?: string;
  translations?: string;
}

export default function LanguageAddEdit({
  language,
  onSave,
  onCancel,
  mode = language ? 'edit' : 'add'
}: LanguageAddEditProps) {
  const [formData, setFormData] = useState({
    name: '',
    country_code: '',
    language_code: '',
    native_name: '',
    region: '',
    is_active: true
  });

  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [activeTab, setActiveTab] = useState<'basic' | 'translations' | 'excel' | 'sections'>('basic');

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Default translation structure
  const defaultTranslations = {
    header: {
      helpline: "Helpline",
      menu: "Menu"
    },
    home: {
      search: "Search",
      location: "Location"
    },
    search: {
      rating: "Rating",
      hotel: "Hotel"
    },
    detail: {
      facilities: "Facilities",
      propertyTypes: "Property Types"
    },
    preview: {
      overview: "Overview",
      readMore: "Read More"
    },
    confirmation: {
      success: "Success",
      failed: "Failed"
    },
    itinerary: {
      confirmed: "Confirmed",
      pending: "Pending"
    },
    profile: {
      name: "Name",
      email: "Email"
    },
    login: {
      name: "Name",
      password: "Password"
    },
    common: {
      room: "Room",
      child: "Child"
    }
  };

  // Initialize form data
  useEffect(() => {
    if (language && mode === 'edit') {
      setFormData({
        name: language.name || '',
        country_code: language.country_code || '',
        language_code: language.language_code || '',
        native_name: language.json?.native_name || '',
        region: language.json?.region || '',
        is_active: language.is_active
      });

      // Extract translations from the json field, excluding metadata
      const { native_name, region, ...translationData } = language.json || {};
      setTranslations(Object.keys(translationData).length > 0 ? translationData : defaultTranslations);
    } else {
      // For new languages, start with default translations
      setTranslations(defaultTranslations);
    }
  }, [language, mode]);

  // Handle input changes
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Validate translations JSON
  const validateTranslations = (): boolean => {
    try {
      // Check if translations object is valid
      if (!translations || typeof translations !== 'object') {
        setErrors(prev => ({ ...prev, translations: 'Invalid translation data' }));
        return false;
      }

      // Check for empty values
      const hasEmptyValues = (obj: any): boolean => {
        for (const [key, value] of Object.entries(obj)) {
          if (typeof value === 'object' && value !== null) {
            if (hasEmptyValues(value)) return true;
          } else if (!value || String(value).trim() === '') {
            return true;
          }
        }
        return false;
      };

      if (hasEmptyValues(translations)) {
        setErrors(prev => ({ ...prev, translations: 'All translation values must be filled' }));
        return false;
      }

      return true;
    } catch (error) {
      setErrors(prev => ({ ...prev, translations: 'Invalid translation format' }));
      return false;
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Name validation
    if (!formData.name.trim()) {
      newErrors.name = 'Language name is required';
    } else if (formData.name.length < LanguageValidation.name.minLength) {
      newErrors.name = `Name must be at least ${LanguageValidation.name.minLength} characters`;
    } else if (formData.name.length > LanguageValidation.name.maxLength) {
      newErrors.name = `Name must not exceed ${LanguageValidation.name.maxLength} characters`;
    } else if (!LanguageValidation.name.pattern.test(formData.name)) {
      newErrors.name = 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }

    // Country code validation
    if (!formData.country_code.trim()) {
      newErrors.country_code = 'Country code is required';
    } else if (!LanguageValidation.country_code.pattern.test(formData.country_code.toUpperCase())) {
      newErrors.country_code = 'Country code must be 2 uppercase letters (e.g., US, IN, GB)';
    }

    // Language code validation
    if (!formData.language_code.trim()) {
      newErrors.language_code = 'Language code is required';
    } else if (!LanguageValidation.language_code.pattern.test(formData.language_code.toLowerCase())) {
      newErrors.language_code = 'Language code must be 2-3 lowercase letters (e.g., en, es, mal)';
    }

    setErrors(newErrors);
    const basicValidation = Object.keys(newErrors).length === 0;
    const translationValidation = validateTranslations();

    return basicValidation && translationValidation;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const languageData = {
        name: formData.name.trim(),
        country_code: formData.country_code.toUpperCase(),
        language_code: formData.language_code.toLowerCase(),
        json: {
          native_name: formData.native_name.trim() || undefined,
          region: formData.region.trim() || undefined,
          ...translations // Include all translation data
        },
        is_active: formData.is_active
      };

      await onSave(languageData);
    } catch (error) {
      console.error('Error saving language:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            type="button"
            onClick={() => setActiveTab('basic')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'basic'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Basic Information
          </button>
          {/* <button
            type="button"
            onClick={() => setActiveTab('translations')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'translations'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Translations
          </button> */}
          {/* <button
            type="button"
            onClick={() => setActiveTab('excel')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'excel'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <i className="ri-file-excel-2-line mr-1"></i>
            Excel Management
          </button> */}
          <button
            type="button"
            onClick={() => setActiveTab('sections')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'sections'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <i className="ri-folder-line mr-1"></i>
            Section Manager
          </button>
        </nav>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information Tab */}
        {activeTab === 'basic' && (
          <div className="space-y-6">
            {/* Language Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Language Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter language name"
                required
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name}</p>
              )}
            </div>

            {/* Country Code */}
            <div>
              <label htmlFor="country_code" className="block text-sm font-medium text-gray-700 mb-2">
                Country Code *
              </label>
              <select
                id="country_code"
                name="country_code"
                value={formData.country_code}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                required
              >
                <option value="">Select Country Code</option>
                {CommonCountryCodes.map((country) => (
                  <option key={country} value={country}>
                    {country}
                  </option>
                ))}
              </select>
              {errors.country_code && (
                <p className="mt-1 text-sm text-red-600">{errors.country_code}</p>
              )}
            </div>

            {/* Language Code */}
            <div>
              <label htmlFor="language_code" className="block text-sm font-medium text-gray-700 mb-2">
                Language Code *
              </label>
              <select
                id="language_code"
                name="language_code"
                value={formData.language_code}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                required
              >
                <option value="">Select Language Code</option>
                {CommonLanguageCodes.map((language) => (
                  <option key={language} value={language}>
                    {language}
                  </option>
                ))}
              </select>
              {errors.language_code && (
                <p className="mt-1 text-sm text-red-600">{errors.language_code}</p>
              )}
            </div>

            {/* Status */}
            <div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="rounded border-gray-300 text-purple-600 shadow-sm focus:border-purple-300 focus:ring focus:ring-purple-200 focus:ring-opacity-50"
                />
                <span className="ml-2 text-sm text-gray-700">Active</span>
              </label>
            </div>
          </div>
        )}

        {/* Translations Tab */}
        {activeTab === 'translations' && (
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Translation Management</h3>
              <p className="text-sm text-gray-600 mb-4">
                Manage translations for this language. You can edit the translation values while keeping the keys intact.
              </p>

              {/* Translation Validation Errors */}
              {errors.translations && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-sm text-red-600">{errors.translations}</p>
                </div>
              )}

              <TranslationEditor
                value={translations}
                onChange={setTranslations}
                readOnly={false}
              />
            </div>
          </div>
        )}

        {/* Excel Management Tab */}
        {activeTab === 'excel' && (
          <div className="space-y-6">
            <ExcelTranslationManager
              value={translations}
              onChange={setTranslations}
              readOnly={false}
              languageName={formData.name || 'Language'}
              languageId={language?.ID}
              onTranslationsSaved={(success, message) => {
                if (success) {
                  // Show success message or refresh data if needed
                  console.log('Translations saved successfully:', message);
                } else {
                  // Show error message
                  console.error('Failed to save translations:', message);
                }
              }}
            />
          </div>
        )}

        {/* Section Manager Tab */}
        {activeTab === 'sections' && (
          <div className="space-y-6">
            <SectionWiseManager
              value={translations}
              onChange={setTranslations}
              readOnly={false}
              languageName={formData.name || 'Language'}
              languageId={language?.ID}
              onTranslationsSaved={(success, message) => {
                if (success) {
                  // Show success message or refresh data if needed
                  console.log('Section saved successfully:', message);
                } else {
                  // Show error message
                  console.error('Failed to save section:', message);
                }
              }}
            />
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <span className="flex items-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving...
              </span>
            ) : (
              mode === 'edit' ? 'Update Language' : 'Create Language'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
