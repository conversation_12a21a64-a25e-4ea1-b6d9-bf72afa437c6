'use client';

import { useState, useEffect } from 'react';
import { Currency, CurrencyFormData, COMMON_CURRENCIES } from '../currency.model';
import CurrencyService from '../currency.service';

interface CurrencyAddEditProps {
  currency: Currency | null;
  onSave: () => void;
  onCancel: () => void;
}

export default function CurrencyAddEdit({ currency, onSave, onCancel }: CurrencyAddEditProps) {
  const [formData, setFormData] = useState<CurrencyFormData>({
    currency_name: '',
    currency_symbol: '',
    currency_symbol_on_right: false,
    is_disabled_conversion: false,
    is_disabled_currency: false,
    from_currency_code: '',
    to_currency_code: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [availableCurrencies, setAvailableCurrencies] = useState<typeof COMMON_CURRENCIES>([]);

  useEffect(() => {
    if (currency) {
      setFormData({
        currency_name: currency.currency_name,
        currency_symbol: currency.currency_symbol,
        currency_symbol_on_right: currency.currency_symbol_on_right,
        is_disabled_conversion: currency.is_disabled_conversion,
        is_disabled_currency: currency.is_disabled_currency,
        from_currency_code: currency.from_currency_code,
        to_currency_code: currency.to_currency_code
      });
    }
  }, [currency]);

  const handleInputChange = (field: keyof CurrencyFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.currency_name.trim()) {
      newErrors.currency_name = 'Currency name is required';
    }

    if (!formData.currency_symbol.trim()) {
      newErrors.currency_symbol = 'Currency symbol is required';
    }

    if (!formData.to_currency_code.trim()) {
      newErrors.to_currency_code = 'Currency code is required';
    } else if (!/^[A-Z]{3}$/.test(formData.to_currency_code)) {
      newErrors.to_currency_code = 'Currency code must be 3 uppercase letters (ISO 4217)';
    }

    if (!formData.from_currency_code.trim()) {
      newErrors.from_currency_code = 'From currency code is required';
    } else if (!/^[A-Z]{3}$/.test(formData.from_currency_code)) {
      newErrors.from_currency_code = 'From currency code must be 3 uppercase letters (ISO 4217)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      if (currency) {
        await CurrencyService.updateCurrency(currency.id, formData);
      } else {
        await CurrencyService.createCurrency(formData);
      }
      onSave();
    } catch (error: any) {
      setErrors({ submit: error.message || 'An error occurred while saving the currency' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Note about rate editing */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <p className="text-blue-800 text-sm">
          <i className="ri-information-line mr-2"></i>
          Note: Exchange rates are automatically managed by the system and cannot be edited manually.
        </p>
      </div>

      {/* Currency Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Currency Name *
        </label>
        <input
          type="text"
          value={formData.currency_name}
          onChange={(e) => handleInputChange('currency_name', e.target.value)}
          placeholder="US Dollar"
          className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm ${
            errors.currency_name ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.currency_name && <p className="text-red-500 text-xs mt-1">{errors.currency_name}</p>}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* To Currency Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Currency Code *
          </label>
          <input
            type="text"
            value={formData.to_currency_code}
            onChange={(e) => handleInputChange('to_currency_code', e.target.value.toUpperCase())}
            placeholder="USD"
            maxLength={3}
            className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm ${
              errors.to_currency_code ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={!!currency} // Disable editing code for existing currencies
          />
          {errors.to_currency_code && <p className="text-red-500 text-xs mt-1">{errors.to_currency_code}</p>}
        </div>

        {/* From Currency Code */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            From Currency Code *
          </label>
          <input
            type="text"
            value={formData.from_currency_code}
            onChange={(e) => handleInputChange('from_currency_code', e.target.value.toUpperCase())}
            placeholder="BHD"
            maxLength={3}
            className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm ${
              errors.from_currency_code ? 'border-red-300' : 'border-gray-300'
            }`}
            disabled={!!currency} // Disable editing code for existing currencies
          />
          {errors.from_currency_code && <p className="text-red-500 text-xs mt-1">{errors.from_currency_code}</p>}
        </div>
      </div>

      {/* Currency Symbol */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Currency Symbol *
        </label>
        <input
          type="text"
          value={formData.currency_symbol}
          onChange={(e) => handleInputChange('currency_symbol', e.target.value)}
          placeholder="$"
          className={`block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm ${
            errors.currency_symbol ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        {errors.currency_symbol && <p className="text-red-500 text-xs mt-1">{errors.currency_symbol}</p>}
      </div>

      {/* Symbol Position */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Symbol Position
        </label>
        <div className="flex items-center space-x-6">
          <div className="flex items-center">
            <input
              type="radio"
              id="symbolLeft"
              name="symbolPosition"
              checked={!formData.currency_symbol_on_right}
              onChange={() => handleInputChange('currency_symbol_on_right', false)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <label htmlFor="symbolLeft" className="ml-2 block text-sm text-gray-700">
              Left (e.g., $100)
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="radio"
              id="symbolRight"
              name="symbolPosition"
              checked={formData.currency_symbol_on_right}
              onChange={() => handleInputChange('currency_symbol_on_right', true)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
            />
            <label htmlFor="symbolRight" className="ml-2 block text-sm text-gray-700">
              Right (e.g., 100€)
            </label>
          </div>
        </div>
      </div>

      {/* Status Checkboxes */}
      <div className="space-y-4">
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDisabledCurrency"
            checked={formData.is_disabled_currency}
            onChange={(e) => handleInputChange('is_disabled_currency', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isDisabledCurrency" className="ml-2 block text-sm text-gray-700">
            Disable Currency
          </label>
          <p className="ml-2 text-xs text-gray-500">(Currency will not be available for use)</p>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDisabledConversion"
            checked={formData.is_disabled_conversion}
            onChange={(e) => handleInputChange('is_disabled_conversion', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isDisabledConversion" className="ml-2 block text-sm text-gray-700">
            Disable Conversion
          </label>
          <p className="ml-2 text-xs text-gray-500">(Currency conversion will be disabled)</p>
        </div>
      </div>

      {/* Submit Error */}
      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <p className="text-red-600 text-sm">{errors.submit}</p>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </div>
          ) : (
            'Update Currency'
          )}
        </button>
      </div>
    </form>
  );
}
