import apiService from "../api/api-service";
import {
  PrivacyPolicies,
  CreatePrivacyPoliciesRequest,
  UpdatePrivacyPoliciesRequest,
  SinglePrivacyPoliciesResponse,
  MultiplePrivacyPoliciesResponse,
  PrivacyPoliciesFilters
} from "./privacy-policies.model";

const BASE_URL = '/privacy-policy';

// Get all privacy policies
export const getAllPrivacyPolicies = async (): Promise<PrivacyPolicies[]> => {
  try {
    const response = await apiService.gettermspolicies<MultiplePrivacyPoliciesResponse>(BASE_URL);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch privacy policies');
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error fetching privacy policies:', error.message);
    } else {
      console.error('Error fetching privacy policies:', error);
    }

    // Return mock data as fallback
    return getMockPrivacyPolicies();
  }
};

// Get privacy policies by language code
export const getPrivacyPoliciesByLanguage = async (languageCode: string): Promise<PrivacyPolicies> => {
  try {
    const response = await apiService.gettermspolicies<SinglePrivacyPoliciesResponse>(`${BASE_URL}/language/${languageCode}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch privacy policies');
  } catch (error: unknown) {
    console.error('Error fetching privacy policies by language:', error);

    // Return mock data as fallback
    const mockData = getMockPrivacyPolicies();
    const found = mockData.find(item => item.language_code === languageCode);
    if (found) {
      return found;
    }
    throw new Error(`Privacy policies not found for language code: ${languageCode}`);
  }
};

// Get privacy policies by ID
export const getPrivacyPoliciesById = async (id: number): Promise<PrivacyPolicies> => {
  try {
    const response = await apiService.gettermspolicies<SinglePrivacyPoliciesResponse>(`${BASE_URL}/${id}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch privacy policies');
  } catch (error: any) {
    console.error('Error fetching privacy policies by ID:', error);

    // Return mock data as fallback
    const mockData = getMockPrivacyPolicies();
    const found = mockData.find(item => item.id === id);
    if (found) {
      return found;
    }
    throw new Error(`Privacy policies not found with ID: ${id}`);
  }
};

// Create new privacy policies
export const createPrivacyPolicies = async (data: CreatePrivacyPoliciesRequest): Promise<PrivacyPolicies> => {
  try {
    const response = await apiService.posttermspolicies<SinglePrivacyPoliciesResponse>(BASE_URL, data);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to create privacy policies');
  } catch (error: any) {
    console.error('Error creating privacy policies:', error);
    throw error;
  }
};

// Update privacy policies
export const updatePrivacyPolicies = async (id: number, data: UpdatePrivacyPoliciesRequest): Promise<PrivacyPolicies> => {
  try {
    const response = await apiService.puttermspolicies<SinglePrivacyPoliciesResponse>(`${BASE_URL}/${id}`, data);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to update privacy policies');
  } catch (error: any) {
    console.error('Error updating privacy policies:', error);
    throw error;
  }
};

// Delete privacy policies
export const deletePrivacyPolicies = async (id: number): Promise<void> => {
  try {
    // Note: API service doesn't have deletetermspolicies method, using regular delete
    const response = await apiService.delete<{success: boolean, message?: string}>(`${BASE_URL}/${id}`);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete privacy policies');
    }
  } catch (error: any) {
    console.error('Error deleting privacy policies:', error);
    throw error;
  }
};

// Search privacy policies with filters
export const searchPrivacyPolicies = async (filters: PrivacyPoliciesFilters): Promise<PrivacyPolicies[]> => {
  try {
    const params = new URLSearchParams();
    
    if (filters.search) params.append('search', filters.search);
    if (filters.language) params.append('language', filters.language);
    if (filters.language_code) params.append('language_code', filters.language_code);
    if (filters.dateRange.startDate) params.append('start_date', filters.dateRange.startDate);
    if (filters.dateRange.endDate) params.append('end_date', filters.dateRange.endDate);

    const response = await apiService.gettermspolicies<MultiplePrivacyPoliciesResponse>(`${BASE_URL}/search?${params.toString()}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to search privacy policies');
  } catch (error: any) {
    console.error('Error searching privacy policies:', error);
    
    // Return filtered mock data as fallback
    return filterMockPrivacyPolicies(filters);
  }
};

// Mock data for fallback
const getMockPrivacyPolicies = (): PrivacyPolicies[] => [
  {
    id: 1,
    privacy_policy: "This privacy policy explains how we collect, use, and protect your personal information when you use our services. We are committed to protecting your privacy...",
    language: "English",
    language_code: "en",
    created_at: "2025-10-09T13:13:21.584829+05:30",
    updated_at: "2025-10-09T13:13:21.584829+05:30"
  },
  {
    id: 2,
    privacy_policy: "Esta política de privacidad explica cómo recopilamos, usamos y protegemos su información personal cuando usa nuestros servicios. Estamos comprometidos a proteger su privacidad...",
    language: "Spanish",
    language_code: "es",
    created_at: "2025-10-09T13:13:49.55117+05:30",
    updated_at: "2025-10-09T13:13:49.55117+05:30"
  },
  {
    id: 3,
    privacy_policy: "Cette politique de confidentialité explique comment nous collectons, utilisons et protégeons vos informations personnelles lorsque vous utilisez nos services. Nous nous engageons à protéger votre vie privée...",
    language: "French",
    language_code: "fr",
    created_at: "2025-10-09T13:19:24.335935+05:30",
    updated_at: "2025-10-09T13:19:24.335935+05:30"
  },
  {
    id: 4,
    privacy_policy: "Diese Datenschutzrichtlinie erklärt, wie wir Ihre persönlichen Daten sammeln, verwenden und schützen, wenn Sie unsere Dienste nutzen. Wir verpflichten uns, Ihre Privatsphäre zu schützen...",
    language: "German",
    language_code: "de",
    created_at: "2025-10-09T13:19:32.97193+05:30",
    updated_at: "2025-10-09T13:19:32.97193+05:30"
  },
  {
    id: 5,
    privacy_policy: "यह गोपनीयता नीति बताती है कि जब आप हमारी सेवाओं का उपयोग करते हैं तो हम आपकी व्यक्तिगत जानकारी कैसे एकत्र, उपयोग और सुरक्षित करते हैं। हम आपकी गोपनीयता की सुरक्षा के लिए प्रतिबद्ध हैं...",
    language: "Hindi",
    language_code: "hi",
    created_at: "2025-10-09T13:23:28.315721+05:30",
    updated_at: "2025-10-09T13:23:28.315721+05:30"
  }
];

// Filter mock data based on filters
const filterMockPrivacyPolicies = (filters: PrivacyPoliciesFilters): PrivacyPolicies[] => {
  let filtered = getMockPrivacyPolicies();

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(item =>
      item.privacy_policy.toLowerCase().includes(searchLower) ||
      item.language.toLowerCase().includes(searchLower) ||
      item.language_code.toLowerCase().includes(searchLower)
    );
  }

  if (filters.language) {
    filtered = filtered.filter(item => item.language === filters.language);
  }

  if (filters.language_code) {
    filtered = filtered.filter(item => item.language_code === filters.language_code);
  }

  return filtered;
};
