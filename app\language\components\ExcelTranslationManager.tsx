'use client';

import { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { updateLanguageTranslations } from '../language.service';

interface ExcelTranslationManagerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  readOnly?: boolean;
  languageName?: string;
  languageId?: number;
  onTranslationsSaved?: (success: boolean, message: string) => void;
}

interface TranslationRow {
  section: string;
  key: string;
  fullPath: string;
  value: string;
}

export default function ExcelTranslationManager({
  value,
  onChange,
  readOnly = false,
  languageName = 'Language',
  languageId,
  onTranslationsSaved
}: ExcelTranslationManagerProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadStatus, setUploadStatus] = useState<{
    type: 'success' | 'error' | 'info' | null;
    message: string;
  }>({ type: null, message: '' });
  const [autoSave, setAutoSave] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const multiFileInputRef = useRef<HTMLInputElement>(null);
  const keyFileInputRef = useRef<HTMLInputElement>(null);

  // Helper function for auto-save
  const handleAutoSave = async (translations: Record<string, any>, fileCount: number) => {
    try {
      setUploadStatus({
        type: 'info',
        message: 'Saving translations to server...'
      });

      await updateLanguageTranslations(languageId!, translations);

      setUploadStatus({
        type: 'success',
        message: `${fileCount} Excel file(s) imported and saved successfully!`
      });

      onTranslationsSaved?.(true, 'Translations saved successfully');

    } catch (saveError) {
      console.error('Auto-save error:', saveError);
      setUploadStatus({
        type: 'error',
        message: `Excel file(s) imported but failed to save to server. Changes are local only.`
      });

      onTranslationsSaved?.(false, 'Failed to save translations to server');
    }
  };

  // Flatten nested JSON structure for Excel export
  const flattenForExcel = (obj: Record<string, any>, path: string[] = []): TranslationRow[] => {
    const rows: TranslationRow[] = [];
    
    Object.entries(obj).forEach(([key, val]) => {
      const currentPath = [...path, key];
      
      if (typeof val === 'object' && val !== null && !Array.isArray(val)) {
        // Nested object - recursively flatten
        rows.push(...flattenForExcel(val, currentPath));
      } else {
        // Leaf value - add as translation row
        rows.push({
          section: currentPath[0] || 'root',
          key: key,
          fullPath: currentPath.join('.'),
          value: String(val || '')
        });
      }
    });
    
    return rows;
  };

  // Rebuild nested structure from Excel data
  const buildNestedFromExcel = (rows: TranslationRow[]): Record<string, any> => {
    const result: Record<string, any> = {};
    
    rows.forEach(row => {
      if (!row.fullPath || row.fullPath.trim() === '') return;
      
      const pathParts = row.fullPath.split('.');
      let current = result;
      
      // Navigate to the correct nested position
      for (let i = 0; i < pathParts.length - 1; i++) {
        const pathKey = pathParts[i];
        if (!current[pathKey]) {
          current[pathKey] = {};
        }
        current = current[pathKey];
      }
      
      // Set the final value
      const finalKey = pathParts[pathParts.length - 1];
      current[finalKey] = row.value || '';
    });
    
    return result;
  };

  // Export all translations to a single Excel file
  const exportAllToExcel = () => {
    try {
      setIsProcessing(true);

      const rows = flattenForExcel(value);

      // Prepare data for Excel
      const excelData = rows.map(row => ({
        'Section': row.section,
        'Key': row.key,
        'Full Path': row.fullPath,
        'Translation Value': row.value,
        'Notes': '' // Empty column for user notes
      }));

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);

      // Set column widths
      const colWidths = [
        { wch: 15 }, // Section
        { wch: 20 }, // Key
        { wch: 30 }, // Full Path
        { wch: 40 }, // Translation Value
        { wch: 20 }  // Notes
      ];
      ws['!cols'] = colWidths;

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, 'All_Translations');

      // Generate Excel file and download
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      const fileName = `${languageName.replace(/[^a-zA-Z0-9]/g, '_')}_ALL_translations_${new Date().toISOString().split('T')[0]}.xlsx`;
      saveAs(data, fileName);

      setUploadStatus({
        type: 'success',
        message: `Complete Excel file exported successfully! ${rows.length} translations exported.`
      });

    } catch (error) {
      console.error('Export error:', error);
      setUploadStatus({
        type: 'error',
        message: 'Failed to export Excel file. Please try again.'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Export translations by individual keys (separate Excel file for each key)
  const exportByKeys = () => {
    try {
      setIsProcessing(true);

      const rows = flattenForExcel(value);
      let exportedCount = 0;

      // Create and download a separate Excel file for each key
      rows.forEach((row, index) => {
        setTimeout(() => {
          // Prepare data for this key
          const excelData = [{
            'Section': row.section,
            'Key': row.key,
            'Full Path': row.fullPath,
            'Translation Value': row.value,
            'Notes': '' // Empty column for user notes
          }];

          // Create workbook and worksheet
          const wb = XLSX.utils.book_new();
          const ws = XLSX.utils.json_to_sheet(excelData);

          // Set column widths
          const colWidths = [
            { wch: 20 }, // Section
            { wch: 25 }, // Key
            { wch: 35 }, // Full Path
            { wch: 45 }, // Translation Value
            { wch: 25 }  // Notes
          ];
          ws['!cols'] = colWidths;

          // Add key info at the top
          XLSX.utils.sheet_add_aoa(ws, [
            [`Translation Key: ${row.key.toUpperCase()}`],
            [`Section: ${row.section}`],
            [`Full Path: ${row.fullPath}`],
            [`Generated: ${new Date().toLocaleString()}`],
            [] // Empty row before data
          ], { origin: 'A1' });

          // Add worksheet to workbook
          XLSX.utils.book_append_sheet(wb, ws, row.key);

          // Generate Excel file and download
          const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
          const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

          const fileName = `${languageName.replace(/[^a-zA-Z0-9]/g, '_')}_KEY_${row.key.toUpperCase()}_${new Date().toISOString().split('T')[0]}.xlsx`;
          saveAs(data, fileName);

          exportedCount++;

          // Update status when all files are exported
          if (exportedCount === rows.length) {
            setUploadStatus({
              type: 'success',
              message: `${rows.length} individual key files exported successfully! Check your downloads folder.`
            });
            setIsProcessing(false);
          }
        }, index * 300); // Stagger downloads to avoid browser blocking
      });

    } catch (error) {
      console.error('Export error:', error);
      setUploadStatus({
        type: 'error',
        message: 'Failed to export key files. Please try again.'
      });
      setIsProcessing(false);
    }
  };

  // Export translations by section (separate Excel file for each section)
  const exportBySections = () => {
    try {
      setIsProcessing(true);

      const rows = flattenForExcel(value);

      // Group rows by section
      const sectionGroups = rows.reduce((groups, row) => {
        if (!groups[row.section]) {
          groups[row.section] = [];
        }
        groups[row.section].push(row);
        return groups;
      }, {} as Record<string, TranslationRow[]>);

      const sections = Object.keys(sectionGroups);
      let exportedCount = 0;

      // Create and download a separate Excel file for each section
      sections.forEach((sectionName, index) => {
        setTimeout(() => {
          const sectionRows = sectionGroups[sectionName];

          // Prepare data for this section
          const excelData = sectionRows.map(row => ({
            'Key': row.key,
            'Full Path': row.fullPath,
            'Translation Value': row.value,
            'Notes': '' // Empty column for user notes
          }));

          // Create workbook and worksheet
          const wb = XLSX.utils.book_new();
          const ws = XLSX.utils.json_to_sheet(excelData);

          // Set column widths
          const colWidths = [
            { wch: 25 }, // Key
            { wch: 35 }, // Full Path
            { wch: 45 }, // Translation Value
            { wch: 25 }  // Notes
          ];
          ws['!cols'] = colWidths;

          // Add section info at the top
          XLSX.utils.sheet_add_aoa(ws, [
            [`Section: ${sectionName.toUpperCase()}`],
            [`Total Items: ${sectionRows.length}`],
            [`Generated: ${new Date().toLocaleString()}`],
            [] // Empty row before data
          ], { origin: 'A1' });

          // Add worksheet to workbook
          XLSX.utils.book_append_sheet(wb, ws, sectionName);

          // Generate Excel file and download
          const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
          const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

          const fileName = `${languageName.replace(/[^a-zA-Z0-9]/g, '_')}_${sectionName.toUpperCase()}_${new Date().toISOString().split('T')[0]}.xlsx`;
          saveAs(data, fileName);

          exportedCount++;

          // Update status when all files are exported
          if (exportedCount === sections.length) {
            setUploadStatus({
              type: 'success',
              message: `${sections.length} section files exported successfully! Check your downloads folder.`
            });
            setIsProcessing(false);
          }
        }, index * 500); // Stagger downloads to avoid browser blocking
      });

    } catch (error) {
      console.error('Export error:', error);
      setUploadStatus({
        type: 'error',
        message: 'Failed to export section files. Please try again.'
      });
      setIsProcessing(false);
    }
  };

  // Handle key-wise file upload (multiple individual key files)
  const handleKeyFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsProcessing(true);
    setUploadStatus({ type: 'info', message: `Processing ${files.length} key file(s)...` });

    const fileArray = Array.from(files);
    const mergedTranslations = { ...value };
    let processedCount = 0;
    const errors: string[] = [];
    const processedKeys: string[] = [];

    fileArray.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Get first worksheet
          const worksheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[worksheetName];

          // Convert to JSON (skip header rows)
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { range: 5 }) as any[];

          // Detect key from filename
          const fileName = file.name.toLowerCase();
          let detectedKey = '';

          // Try to extract key from filename pattern: {Language}_KEY_{KEYNAME}_{Date}.xlsx
          const keyMatch = fileName.match(/_key_([a-z]+)_\d{4}-\d{2}-\d{2}\.xlsx?$/);
          if (keyMatch) {
            detectedKey = keyMatch[1].toLowerCase();
          } else {
            detectedKey = worksheetName.toLowerCase();
          }

          // Process the single key data
          if (jsonData.length > 0) {
            const row = jsonData[0]; // Should only be one row for key files

            if (!row['Full Path'] || row['Full Path'].toString().trim() === '') {
              errors.push(`${file.name}: Missing 'Full Path'`);
              processedCount++;
              return;
            }

            if (row['Translation Value'] === undefined || row['Translation Value'] === null) {
              row['Translation Value'] = '';
            }

            const translationRow: TranslationRow = {
              section: row['Section'] || 'unknown',
              key: detectedKey,
              fullPath: row['Full Path'].toString().trim(),
              value: row['Translation Value'].toString()
            };

            // Build nested structure for this key and merge
            const keyStructure = buildNestedFromExcel([translationRow]);
            Object.assign(mergedTranslations, keyStructure);
            processedKeys.push(detectedKey);
          }

          processedCount++;

          // Check if all files are processed
          if (processedCount === fileArray.length) {
            if (errors.length > 0) {
              setUploadStatus({
                type: 'error',
                message: `Some errors found:\n${errors.slice(0, 3).join('\n')}${errors.length > 3 ? `\n... and ${errors.length - 3} more errors` : ''}`
              });
            } else {
              onChange(mergedTranslations);

              // Auto-save if enabled
              if (autoSave && languageId && !readOnly) {
                handleAutoSave(mergedTranslations, fileArray.length);
              } else {
                setUploadStatus({
                  type: 'success',
                  message: `${fileArray.length} key file(s) imported successfully! Updated keys: ${processedKeys.join(', ')}`
                });
              }
            }
            setIsProcessing(false);
          }

        } catch (error) {
          console.error(`Error processing ${file.name}:`, error);
          errors.push(`${file.name}: Failed to process file`);
          processedCount++;

          if (processedCount === fileArray.length) {
            setUploadStatus({
              type: 'error',
              message: `Failed to process some files:\n${errors.join('\n')}`
            });
            setIsProcessing(false);
          }
        }
      };

      reader.readAsArrayBuffer(file);
    });

    // Clear file input
    if (keyFileInputRef.current) {
      keyFileInputRef.current.value = '';
    }
  };

  // Handle multiple file upload (section-based)
  const handleMultipleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsProcessing(true);
    setUploadStatus({ type: 'info', message: `Processing ${files.length} Excel file(s)...` });

    const fileArray = Array.from(files);
    const mergedTranslations = { ...value };
    let processedCount = 0;
    const errors: string[] = [];

    fileArray.forEach((file, index) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          // Get first worksheet
          const worksheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[worksheetName];

          // Convert to JSON (skip header rows)
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { range: 4 }) as any[];

          // Detect section from filename or worksheet name
          const fileName = file.name.toLowerCase();
          let detectedSection = '';

          // Try to extract section from filename
          const sectionMatch = fileName.match(/_([a-z]+)_\d{4}-\d{2}-\d{2}\.xlsx?$/);
          if (sectionMatch) {
            detectedSection = sectionMatch[1].toLowerCase();
          } else {
            detectedSection = worksheetName.toLowerCase();
          }

          // Process the data for this section
          const translationRows: TranslationRow[] = [];

          jsonData.forEach((row, rowIndex) => {
            const actualRowNum = rowIndex + 5; // Account for header rows

            if (!row['Full Path'] || row['Full Path'].toString().trim() === '') {
              errors.push(`${file.name} - Row ${actualRowNum}: Missing 'Full Path'`);
              return;
            }

            if (row['Translation Value'] === undefined || row['Translation Value'] === null) {
              row['Translation Value'] = '';
            }

            translationRows.push({
              section: detectedSection,
              key: row['Key'] || '',
              fullPath: row['Full Path'].toString().trim(),
              value: row['Translation Value'].toString()
            });
          });

          // Build nested structure for this section and merge
          const sectionStructure = buildNestedFromExcel(translationRows);
          Object.assign(mergedTranslations, sectionStructure);

          processedCount++;

          // Check if all files are processed
          if (processedCount === fileArray.length) {
            if (errors.length > 0) {
              setUploadStatus({
                type: 'error',
                message: `Some errors found:\n${errors.slice(0, 3).join('\n')}${errors.length > 3 ? `\n... and ${errors.length - 3} more errors` : ''}`
              });
            } else {
              onChange(mergedTranslations);

              // Auto-save if enabled
              if (autoSave && languageId && !readOnly) {
                handleAutoSave(mergedTranslations, fileArray.length);
              } else {
                setUploadStatus({
                  type: 'success',
                  message: `${fileArray.length} Excel file(s) imported successfully!`
                });
              }
            }
            setIsProcessing(false);
          }

        } catch (error) {
          console.error(`Error processing ${file.name}:`, error);
          errors.push(`${file.name}: Failed to process file`);
          processedCount++;

          if (processedCount === fileArray.length) {
            setUploadStatus({
              type: 'error',
              message: `Failed to process some files:\n${errors.join('\n')}`
            });
            setIsProcessing(false);
          }
        }
      };

      reader.readAsArrayBuffer(file);
    });

    // Clear file input
    if (multiFileInputRef.current) {
      multiFileInputRef.current.value = '';
    }
  };

  // Handle single file upload
  const handleSingleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsProcessing(true);
    setUploadStatus({ type: 'info', message: 'Processing Excel file...' });

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get first worksheet
        const worksheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[worksheetName];
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet) as any[];
        
        // Validate and transform data
        const translationRows: TranslationRow[] = [];
        const errors: string[] = [];
        
        jsonData.forEach((row, index) => {
          const rowNum = index + 2; // Excel row number (1-based + header)
          
          // Check required fields
          if (!row['Full Path'] || row['Full Path'].toString().trim() === '') {
            errors.push(`Row ${rowNum}: Missing 'Full Path'`);
            return;
          }
          
          if (row['Translation Value'] === undefined || row['Translation Value'] === null) {
            row['Translation Value'] = ''; // Allow empty values
          }
          
          translationRows.push({
            section: row['Section'] || 'unknown',
            key: row['Key'] || '',
            fullPath: row['Full Path'].toString().trim(),
            value: row['Translation Value'].toString()
          });
        });

        if (errors.length > 0) {
          setUploadStatus({
            type: 'error',
            message: `Validation errors found:\n${errors.slice(0, 5).join('\n')}${errors.length > 5 ? `\n... and ${errors.length - 5} more errors` : ''}`
          });
          setIsProcessing(false);
          return;
        }

        // Build nested structure and update
        const nestedStructure = buildNestedFromExcel(translationRows);
        onChange(nestedStructure);

        // Auto-save to backend if enabled and languageId is provided
        if (autoSave && languageId && !readOnly) {
          handleAutoSave(nestedStructure, 1);
        } else {
          setUploadStatus({
            type: 'success',
            message: `Excel file imported successfully! ${translationRows.length} translations updated.`
          });
        }

      } catch (error) {
        console.error('Import error:', error);
        setUploadStatus({
          type: 'error',
          message: 'Failed to process Excel file. Please check the file format and try again.'
        });
      } finally {
        setIsProcessing(false);
        // Clear file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      }
    };

    reader.readAsArrayBuffer(file);
  };

  // Clear status message
  const clearStatus = () => {
    setUploadStatus({ type: null, message: '' });
  };

  const translationCount = flattenForExcel(value).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i className="ri-file-excel-2-line text-green-600 text-xl"></i>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Excel Translation Management</h3>
            <p className="text-sm text-gray-600">Export, edit, and import translations using Excel</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3 text-sm">
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center space-x-2">
              <i className="ri-download-line text-blue-500"></i>
              <span className="font-medium">Export All</span>
            </div>
            <p className="text-gray-600 mt-1">Download one complete Excel file with all translations</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center space-x-2">
              <i className="ri-file-list-3-line text-green-500"></i>
              <span className="font-medium">Export Sections</span>
            </div>
            <p className="text-gray-600 mt-1">Download separate Excel files for each section (header, home, etc.)</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center space-x-2">
              <i className="ri-key-2-line text-orange-500"></i>
              <span className="font-medium">Export Keys</span>
            </div>
            <p className="text-gray-600 mt-1">Download individual Excel files for each translation key</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center space-x-2">
              <i className="ri-edit-line text-indigo-500"></i>
              <span className="font-medium">Edit</span>
            </div>
            <p className="text-gray-600 mt-1">Modify translations in Excel - maximum granularity</p>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center space-x-2">
              <i className="ri-upload-line text-purple-500"></i>
              <span className="font-medium">Import</span>
            </div>
            <p className="text-gray-600 mt-1">Upload files at any level - single, section, or key files</p>
          </div>
        </div>
      </div>

      {/* Status Message */}
      {uploadStatus.type && (
        <div className={`p-4 rounded-lg border ${
          uploadStatus.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
          uploadStatus.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
          'bg-blue-50 border-blue-200 text-blue-800'
        }`}>
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-2">
              <i className={`${
                uploadStatus.type === 'success' ? 'ri-check-circle-line' :
                uploadStatus.type === 'error' ? 'ri-error-warning-line' :
                'ri-information-line'
              } text-lg mt-0.5`}></i>
              <div>
                <p className="font-medium">
                  {uploadStatus.type === 'success' ? 'Success' :
                   uploadStatus.type === 'error' ? 'Error' : 'Processing'}
                </p>
                <p className="text-sm whitespace-pre-line">{uploadStatus.message}</p>
              </div>
            </div>
            <button
              onClick={clearStatus}
              className="text-gray-400 hover:text-gray-600"
            >
              <i className="ri-close-line"></i>
            </button>
          </div>
        </div>
      )}

      {/* Auto-save Settings */}
      {!readOnly && languageId && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <i className="ri-save-line text-blue-500 text-lg"></i>
              <div>
                <h4 className="font-medium text-gray-900">Auto-save to Server</h4>
                <p className="text-sm text-gray-600">Automatically save translations to the backend when importing Excel files</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoSave}
                onChange={(e) => setAutoSave(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="space-y-4">
          {/* Export Options */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Export All Button */}
            <button
              onClick={exportAllToExcel}
              disabled={isProcessing || translationCount === 0}
              className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-900 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <i className="ri-download-line text-lg"></i>
              <span className="font-medium">
                {isProcessing ? 'Exporting...' : `Export All (${translationCount} items)`}
              </span>
            </button>

            {/* Export by Sections Button */}
            <button
              onClick={exportBySections}
              disabled={isProcessing || translationCount === 0}
              className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-900 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <i className="ri-file-list-3-line text-lg"></i>
              <span className="font-medium">
                {isProcessing ? 'Exporting...' : `Export by Sections`}
              </span>
            </button>

            {/* Export by Keys Button */}
            <button
              onClick={exportByKeys}
              disabled={isProcessing || translationCount === 0}
              className="flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-900 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <i className="ri-key-2-line text-lg"></i>
              <span className="font-medium">
                {isProcessing ? 'Exporting...' : `Export by Keys`}
              </span>
            </button>
          </div>

          {/* Import Options */}
          {!readOnly && (
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">Import Options</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Single File Import */}
                <div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleSingleFileUpload}
                    disabled={isProcessing}
                    className="hidden"
                  />
                  <button
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <i className="ri-upload-line text-lg"></i>
                    <span className="font-medium">
                      {isProcessing ? 'Processing...' : 'Import Single File'}
                    </span>
                  </button>
                  <p className="text-xs text-gray-500 mt-1">Import one complete Excel file</p>
                </div>

                {/* Multiple Section Files Import */}
                <div>
                  <input
                    ref={multiFileInputRef}
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleMultipleFileUpload}
                    disabled={isProcessing}
                    multiple
                    className="hidden"
                  />
                  <button
                    onClick={() => multiFileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <i className="ri-file-list-3-line text-lg"></i>
                    <span className="font-medium">
                      {isProcessing ? 'Processing...' : 'Import Section Files'}
                    </span>
                  </button>
                  <p className="text-xs text-gray-500 mt-1">Import multiple section files at once</p>
                </div>

                {/* Multiple Key Files Import */}
                <div>
                  <input
                    ref={keyFileInputRef}
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleKeyFileUpload}
                    disabled={isProcessing}
                    multiple
                    className="hidden"
                  />
                  <button
                    onClick={() => keyFileInputRef.current?.click()}
                    disabled={isProcessing}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <i className="ri-key-2-line text-lg"></i>
                    <span className="font-medium">
                      {isProcessing ? 'Processing...' : 'Import Key Files'}
                    </span>
                  </button>
                  <p className="text-xs text-gray-500 mt-1">Import multiple individual key files</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">How to Use:</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Export Instructions */}
            <div>
              <h5 className="font-medium text-gray-800 mb-2">📤 Export Options:</h5>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li><strong>Export All:</strong> Download one Excel file with all translations</li>
                <li><strong>Export by Sections:</strong> Download separate Excel files for each section (header, home, search, etc.)</li>
                <li><strong>Export by Keys:</strong> Download individual Excel files for each translation key (helpline, menu, search, etc.)</li>
              </ul>
            </div>

            {/* Import Instructions */}
            <div>
              <h5 className="font-medium text-gray-800 mb-2">📥 Import Options:</h5>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li><strong>Import Single File:</strong> Upload one complete Excel file</li>
                <li><strong>Import Section Files:</strong> Upload multiple section files at once</li>
                <li><strong>Import Key Files:</strong> Upload multiple individual key files at once</li>
              </ul>
            </div>
          </div>

          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <h5 className="font-medium text-blue-900 mb-2">✏️ Editing Guidelines:</h5>
            <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
              <li>Only edit the "Translation Value" column in Excel</li>
              <li>Do not modify "Section", "Key", or "Full Path" columns</li>
              <li>Save your Excel file(s) before importing</li>
              <li>The system will automatically validate and apply changes</li>
              <li>Use auto-save to automatically update the server</li>
            </ol>
          </div>

          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <h5 className="font-medium text-green-900 mb-1">💡 Pro Tips:</h5>
            <ul className="text-sm text-green-800 space-y-1 list-disc list-inside">
              <li><strong>Key-wise:</strong> Perfect for individual key updates - each translator gets one specific key to work on</li>
              <li><strong>Section-wise:</strong> Good for team collaboration - assign different sections to different translators</li>
              <li><strong>Complete file:</strong> Best for comprehensive reviews or small teams</li>
              <li>You can mix and match - import key files and section files together!</li>
            </ul>
          </div>

          <div className="mt-3 p-3 bg-orange-50 border border-orange-200 rounded-lg">
            <h5 className="font-medium text-orange-900 mb-1">🔑 Key-wise Workflow Benefits:</h5>
            <ul className="text-sm text-orange-800 space-y-1 list-disc list-inside">
              <li>Maximum granularity - one file per translation key</li>
              <li>Perfect for distributed teams - assign specific keys to specific people</li>
              <li>Minimal conflicts - each person works on completely separate files</li>
              <li>Easy tracking - know exactly who worked on which translation key</li>
              <li>Quick updates - modify just one key without affecting others</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
