'use client';

import { useState, useEffect, useCallback } from 'react';
import { Language, LanguageFilters, CreateLanguageRequest, UpdateLanguageRequest } from '../language.model';
import LanguageService from '../language.service';
import LanguageList from './LanguageList';
import LanguageAddEdit from './LanguageAddEdit';
import LanguageFiltersComponent from './LanguageFilters';
import TranslationViewer from './TranslationViewer';
import ExcelTranslationManager from './ExcelTranslationManager';
import SectionWiseManager from './SectionWiseManager';
import Modal from '../../components/ui/Modal';
import PageSectionHeader from '../../components/ui/PageSectionHeader';

export default function LanguageMaster() {
  // State management
  const [languages, setLanguages] = useState<Language[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLanguage, setSelectedLanguage] = useState<Language | null>(null);
  const [isAddEditOpen, setIsAddEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');
  const [viewTab, setViewTab] = useState<'viewer' | 'excel' | 'sections'>('viewer');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);

  // Filter state
  const [filters, setFilters] = useState<LanguageFilters>({});

  // Load languages
  const loadLanguages = useCallback(async () => {
    try {
      setLoading(true);
      const response = await LanguageService.getAllLanguages(currentPage, pageSize, filters);
      
      if (response.success) {
        setLanguages(response.data);
        setTotalItems(response.total);
        setTotalPages(Math.ceil(response.total / pageSize));
      }
    } catch (error) {
      console.error('Error loading languages:', error);
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize, filters]);

  // Load languages on component mount and when dependencies change
  useEffect(() => {
    loadLanguages();
  }, [loadLanguages]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1); // Reset to first page
  };

  // Handle filter change
  const handleFilterChange = (newFilters: LanguageFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle add language
  const handleAddLanguage = () => {
    setSelectedLanguage(null);
    setFormMode('add');
    setIsAddEditOpen(true);
  };

  // Handle edit language
  const handleEditLanguage = (language: Language) => {
    setSelectedLanguage(language);
    setFormMode('edit');
    setIsAddEditOpen(true);
  };

  // Handle view language
  const handleViewLanguage = (language: Language) => {
    setSelectedLanguage(language);
    setIsViewOpen(true);
  };

  // Handle delete language
  const handleDeleteLanguage = async (language: Language) => {
    if (window.confirm(`Are you sure you want to delete "${language.name}"? This action cannot be undone.`)) {
      try {
        await LanguageService.deleteLanguage(language.ID);
        await loadLanguages(); // Reload the list
      } catch (error) {
        console.error('Error deleting language:', error);
        alert('Failed to delete language. Please try again.');
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (language: Language) => {
    try {
      await LanguageService.toggleLanguageStatus(language.ID);
      await loadLanguages(); // Reload the list
    } catch (error) {
      console.error('Error toggling language status:', error);
      alert('Failed to update language status. Please try again.');
    }
  };

  // Handle save language (add/edit)
  const handleSaveLanguage = async (languageData: CreateLanguageRequest | UpdateLanguageRequest) => {
    try {
      if (formMode === 'add') {
        await LanguageService.createLanguage(languageData as CreateLanguageRequest);
      } else if (selectedLanguage) {
        await LanguageService.updateLanguage(selectedLanguage.ID, languageData as UpdateLanguageRequest);
      }
      
      setIsAddEditOpen(false);
      setSelectedLanguage(null);
      await loadLanguages(); // Reload the list
    } catch (error) {
      console.error('Error saving language:', error);
      alert('Failed to save language. Please try again.');
    }
  };

  // Handle cancel add/edit
  const handleCancelAddEdit = () => {
    setIsAddEditOpen(false);
    setSelectedLanguage(null);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageSectionHeader
        title="Language Management"
        subtitle="Manage system languages and localization settings"
        totalItems={totalItems}
        showAddButton={true}
        addButtonText="Add Language"
        onAddButtonClick={handleAddLanguage}
      />

      {/* Filters */}
      <LanguageFiltersComponent
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={() => handleFilterChange({})}
      />

      {/* Language List */}
      <LanguageList
        languages={languages}
        onEdit={handleEditLanguage}
        onView={handleViewLanguage}
        onDelete={handleDeleteLanguage}
        onToggleStatus={handleToggleStatus}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isAddEditOpen}
        onClose={handleCancelAddEdit}
        title={formMode === 'add' ? 'Add New Language' : 'Edit Language'}
        subtitle={formMode === 'add' 
          ? 'Create a new language entry for the system' 
          : `Update ${selectedLanguage?.name} details`
        }
        size="lg"
        height="fixed"
      >
        <LanguageAddEdit
          language={selectedLanguage}
          onSave={handleSaveLanguage}
          onCancel={handleCancelAddEdit}
          mode={formMode}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        isOpen={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        title="Language Details"
        subtitle={selectedLanguage ? `View ${selectedLanguage.name} information and translations` : 'Language information'}
        size="xl"
        height="auto"
      >
        {selectedLanguage && (
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Language Name</label>
                  <p className="text-sm text-gray-900">{selectedLanguage.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Native Name</label>
                  <p className="text-sm text-gray-900">{selectedLanguage.json?.native_name || 'Not specified'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Language Code</label>
                  <p className="text-sm text-gray-900 uppercase">{selectedLanguage.language_code}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Country Code</label>
                  <p className="text-sm text-gray-900 uppercase">{selectedLanguage.country_code}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Region</label>
                  <p className="text-sm text-gray-900">{selectedLanguage.json?.region || 'Not specified'}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Status</label>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    selectedLanguage.is_active
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {selectedLanguage.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Timestamps</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500">Created At</label>
                  <p className="text-sm text-gray-900">{new Date(selectedLanguage.CreatedAt).toLocaleString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-500">Updated At</label>
                  <p className="text-sm text-gray-900">{new Date(selectedLanguage.UpdatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* Translation Data */}
            {selectedLanguage.json && Object.keys(selectedLanguage.json).length > 0 && (
              <div className="bg-white rounded-lg border border-gray-200">
                <div className="border-b border-gray-200">
                  <nav className="-mb-px flex space-x-8 px-6">
                    {/* <button
                      type="button"
                      onClick={() => setViewTab('viewer')}
                      className={`py-3 px-1 border-b-2 font-medium text-sm ${
                        viewTab === 'viewer'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <i className="ri-eye-line mr-1"></i>
                      View Translations
                    </button> */}
                    {/* <button
                      type="button"
                      onClick={() => setViewTab('excel')}
                      className={`py-3 px-1 border-b-2 font-medium text-sm ${
                        viewTab === 'excel'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <i className="ri-file-excel-2-line mr-1"></i>
                      Excel Export
                    </button>
                    <button
                      type="button"
                      onClick={() => setViewTab('sections')}
                      className={`py-3 px-1 border-b-2 font-medium text-sm ${
                        viewTab === 'sections'
                          ? 'border-purple-500 text-purple-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <i className="ri-folder-line mr-1"></i>
                      Section Manager
                    </button> */}
                  </nav>
                </div>

                {/* <div className="p-6">
                  {viewTab === 'viewer' && (
                    <TranslationViewer
                      data={(() => {
                        const { native_name, region, ...translations } = selectedLanguage.json || {};
                        return Object.keys(translations).length > 0 ? translations : {};
                      })()}
                      title="Translation Data"
                    />
                  )}

                  {viewTab === 'excel' && (
                    <ExcelTranslationManager
                      value={(() => {
                        const { native_name, region, ...translations } = selectedLanguage.json || {};
                        return Object.keys(translations).length > 0 ? translations : {};
                      })()}
                      onChange={() => {}} // Read-only in view mode
                      readOnly={true}
                      languageName={selectedLanguage.name}
                      languageId={selectedLanguage.ID}
                    />
                  )}

                  {viewTab === 'sections' && (
                    <SectionWiseManager
                      value={(() => {
                        const { native_name, region, ...translations } = selectedLanguage.json || {};
                        return Object.keys(translations).length > 0 ? translations : {};
                      })()}
                      onChange={() => {}} // Read-only in view mode
                      readOnly={true}
                      languageName={selectedLanguage.name}
                      languageId={selectedLanguage.ID}
                    />
                  )}
                </div> */}
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
}
