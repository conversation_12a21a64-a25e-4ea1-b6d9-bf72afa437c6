'use client';

import { useState, useEffect } from 'react';
import { Currency, CurrencyFilters, CurrencyStats } from '../currency.model';
import CurrencyService from '../currency.service';
import CurrencyStatsComponent from './CurrencyStats';
import CurrencyList from './CurrencyList';
import CurrencyAddEdit from './CurrencyAddEdit';
import CurrencyView from './CurrencyView';
import CurrencyFiltersComponent from './CurrencyFilters';
import Modal from '../../components/ui/Modal';

export default function CurrencyMaster() {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);
  const [isAddEditOpen, setIsAddEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [stats, setStats] = useState<CurrencyStats | null>(null);
  const [filters, setFilters] = useState<CurrencyFilters>({
    search: '',
    is_disabled_currency: undefined,
    is_disabled_conversion: undefined,
    currency_code_filter: ''
  });

  // Fetch currencies
  const fetchCurrencies = async () => {
    try {
      setLoading(true);
      const response = await CurrencyService.getAllCurrencies(currentPage, pageSize, filters);
      setCurrencies(response.data);
      setTotalPages(response.pagination.total_pages);
      setTotalItems(response.pagination.total_items);
    } catch (error) {
      console.error('Error fetching currencies:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch stats
  const fetchStats = async () => {
    try {
      const statsData = await CurrencyService.getCurrencyStats();
      setStats(statsData);
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  useEffect(() => {
    fetchCurrencies();
    fetchStats();
  }, [currentPage, pageSize, filters]);

  // Handle edit currency
  const handleEditCurrency = (currency: Currency) => {
    setSelectedCurrency(currency);
    setIsAddEditOpen(true);
  };

  // Handle view currency
  const handleViewCurrency = (currency: Currency) => {
    setSelectedCurrency(currency);
    setIsViewOpen(true);
  };

  // Handle toggle status
  const handleToggleStatus = async (currency: Currency) => {
    try {
      await CurrencyService.toggleCurrencyStatus(currency.id);
      fetchCurrencies();
      fetchStats();
    } catch (error) {
      console.error('Error toggling currency status:', error);
      alert('Error updating currency status. Please try again.');
    }
  };

  // Handle save currency
  const handleSaveCurrency = async () => {
    setIsAddEditOpen(false);
    await fetchCurrencies();
    await fetchStats();
  };

  // Handle filters change
  const handleFiltersChange = (newFilters: CurrencyFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size change
  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* Currency Statistics */}
      <CurrencyStatsComponent stats={stats} loading={loading} />

      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Currency Management</h2>
          <p className="text-gray-600">
            Manage exchange rates and currency configurations
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="bg-blue-50 px-4 py-2 rounded-lg">
            <span className="text-sm font-medium text-blue-700">
              Total: {totalItems} currencies
            </span>
          </div>
        </div>
      </div>

      {/* Filters */}
      <CurrencyFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
      />

      {/* Currency List */}
      <CurrencyList
        currencies={currencies}
        onEdit={handleEditCurrency}
        onView={handleViewCurrency}
        onToggleStatus={handleToggleStatus}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Edit Modal */}
      <Modal
        isOpen={isAddEditOpen}
        onClose={() => setIsAddEditOpen(false)}
        title="Edit Currency"
        subtitle={selectedCurrency ? `Update ${selectedCurrency.currency_name} details` : 'Edit currency settings'}
        size="lg"
      >
        <CurrencyAddEdit
          currency={selectedCurrency}
          onSave={handleSaveCurrency}
          onCancel={() => setIsAddEditOpen(false)}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        isOpen={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        title="Currency Details"
        subtitle={selectedCurrency ? `${selectedCurrency.currency_name} (${selectedCurrency.to_currency_code})` : ''}
        size="lg"
      >
        <CurrencyView
          currency={selectedCurrency}
          onEdit={() => {
            setIsViewOpen(false);
            setIsAddEditOpen(true);
          }}
          onClose={() => setIsViewOpen(false)}
        />
      </Modal>
    </div>
  );
}
