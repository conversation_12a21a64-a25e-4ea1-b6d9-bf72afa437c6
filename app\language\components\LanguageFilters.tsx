'use client';

import { useState, useEffect } from 'react';
import { LanguageFilters, CommonCountryCodes, CommonLanguageCodes } from '../language.model';

interface LanguageFiltersProps {
  filters: LanguageFilters;
  onFilterChange: (filters: LanguageFilters) => void;
  onClearFilters: () => void;
}

export default function LanguageFiltersComponent({
  filters,
  onFilterChange,
  onClearFilters
}: LanguageFiltersProps) {
  const [localFilters, setLocalFilters] = useState<LanguageFilters>(filters);
  const [isExpanded, setIsExpanded] = useState(false);

  // Update local filters when props change
  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  // Handle filter input changes
  const handleFilterChange = (field: keyof LanguageFilters, value: string | boolean | undefined) => {
    const newFilters = {
      ...localFilters,
      [field]: value === '' ? undefined : value
    };
    setLocalFilters(newFilters);
  };

  // Apply filters
  const applyFilters = () => {
    onFilterChange(localFilters);
  };

  // Clear all filters
  const clearFilters = () => {
    setLocalFilters({});
    onClearFilters();
  };

  // Check if any filters are active
  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== null
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Filter Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
            {hasActiveFilters && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {Object.values(filters).filter(v => v !== undefined && v !== '' && v !== null).length} active
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <button
                onClick={clearFilters}
                className="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100"
              >
                Clear All
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="flex items-center text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100"
            >
              {isExpanded ? (
                <>
                  <i className="ri-arrow-up-s-line mr-1"></i>
                  Collapse
                </>
              ) : (
                <>
                  <i className="ri-arrow-down-s-line mr-1"></i>
                  Expand
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Filter Content */}
      {isExpanded && (
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Search
              </label>
              <input
                type="text"
                value={localFilters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search languages..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Country Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Country Code
              </label>
              <select
                value={localFilters.country_code || ''}
                onChange={(e) => handleFilterChange('country_code', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Countries</option>
                {CommonCountryCodes.map(code => (
                  <option key={code} value={code}>{code}</option>
                ))}
              </select>
            </div>

            {/* Language Code */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Language Code
              </label>
              <select
                value={localFilters.language_code || ''}
                onChange={(e) => handleFilterChange('language_code', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Languages</option>
                {CommonLanguageCodes.map(code => (
                  <option key={code} value={code}>{code.toUpperCase()}</option>
                ))}
              </select>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={localFilters.is_active === undefined ? '' : localFilters.is_active.toString()}
                onChange={(e) => handleFilterChange('is_active', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Status</option>
                <option value="true">Active</option>
                <option value="false">Inactive</option>
              </select>
            </div>
          </div>

          {/* Filter Actions */}
          <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={clearFilters}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500"
            >
              Clear Filters
            </button>
            <button
              onClick={applyFilters}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
            >
              Apply Filters
            </button>
          </div>
        </div>
      )}

      {/* Quick Filter Chips (when collapsed) */}
      {!isExpanded && hasActiveFilters && (
        <div className="px-6 py-3 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: {filters.search}
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <i className="ri-close-line text-xs"></i>
                </button>
              </span>
            )}
            {filters.country_code && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Country: {filters.country_code}
                <button
                  onClick={() => handleFilterChange('country_code', '')}
                  className="ml-1 text-green-600 hover:text-green-800"
                >
                  <i className="ri-close-line text-xs"></i>
                </button>
              </span>
            )}
            {filters.language_code && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Language: {filters.language_code.toUpperCase()}
                <button
                  onClick={() => handleFilterChange('language_code', '')}
                  className="ml-1 text-purple-600 hover:text-purple-800"
                >
                  <i className="ri-close-line text-xs"></i>
                </button>
              </span>
            )}
            {filters.is_active !== undefined && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Status: {filters.is_active ? 'Active' : 'Inactive'}
                <button
                  onClick={() => handleFilterChange('is_active', undefined)}
                  className="ml-1 text-yellow-600 hover:text-yellow-800"
                >
                  <i className="ri-close-line text-xs"></i>
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
