'use client';


import { CurrencyFilters as CurrencyFiltersType } from '../currency.model';

interface CurrencyFiltersProps {
  filters: CurrencyFiltersType;
  onFiltersChange: (filters: CurrencyFiltersType) => void;
}

export default function CurrencyFilters({ filters, onFiltersChange }: CurrencyFiltersProps) {

  const handleFilterChange = (key: keyof CurrencyFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      is_disabled_currency: undefined,
      is_disabled_conversion: undefined,
      currency_code_filter: ''
    });
  };

  const hasActiveFilters = () => {
    return filters.search ||
           filters.is_disabled_currency !== undefined ||
           filters.is_disabled_conversion !== undefined ||
           filters.currency_code_filter;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          <input
            type="text"
            placeholder="Search currencies..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full"
          />
        </div>

        {/* Currency Status */}
        <select
          value={filters.is_disabled_currency === undefined ? '' : filters.is_disabled_currency.toString()}
          onChange={(e) => {
            const value = e.target.value;
            handleFilterChange('is_disabled_currency', value === '' ? undefined : value === 'true');
          }}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full pr-8"
        >
          <option value="">All Currency Status</option>
          <option value="false">Active</option>
          <option value="true">Disabled</option>
        </select>

        {/* Conversion Status */}
        <select
          value={filters.is_disabled_conversion === undefined ? '' : filters.is_disabled_conversion.toString()}
          onChange={(e) => {
            const value = e.target.value;
            handleFilterChange('is_disabled_conversion', value === '' ? undefined : value === 'true');
          }}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full pr-8"
        >
          <option value="">All Conversion Status</option>
          <option value="false">Enabled</option>
          <option value="true">Disabled</option>
        </select>

        {/* Currency Code Filter */}
        <input
          type="text"
          placeholder="Currency code (e.g., USD)"
          value={filters.currency_code_filter}
          onChange={(e) => handleFilterChange('currency_code_filter', e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full"
        />
      </div>
    </div>
  );
}
