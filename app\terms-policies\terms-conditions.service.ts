import apiService from "../api/api-service";
import {
  TermsConditions,
  CreateTermsConditionsRequest,
  UpdateTermsConditionsRequest,
  SingleTermsConditionsResponse,
  MultipleTermsConditionsResponse,
  TermsConditionsFilters
} from "./terms-conditions.model";

const BASE_URL = '/terms-conditions';

// Get all terms and conditions
export const getAllTermsConditions = async (): Promise<TermsConditions[]> => {
  try {
    const response = await apiService.gettermspolicies<MultipleTermsConditionsResponse>(BASE_URL);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch terms and conditions');
  } catch (error: any) {
    console.error('Error fetching terms and conditions:', error);

    // Return mock data as fallback
    return getMockTermsConditions();
  }
};

// Get terms and conditions by language code
export const getTermsConditionsByLanguage = async (languageCode: string): Promise<TermsConditions> => {
  try {
    const response = await apiService.gettermspolicies<SingleTermsConditionsResponse>(`${BASE_URL}/language/${languageCode}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch terms and conditions');
  } catch (error: any) {
    console.error('Error fetching terms and conditions by language:', error);

    // Return mock data as fallback
    const mockData = getMockTermsConditions();
    const found = mockData.find(item => item.language_code === languageCode);
    if (found) {
      return found;
    }
    throw new Error(`Terms and conditions not found for language code: ${languageCode}`);
  }
};

// Get terms and conditions by ID
export const getTermsConditionsById = async (id: number): Promise<TermsConditions> => {
  try {
    const response = await apiService.gettermspolicies<SingleTermsConditionsResponse>(`${BASE_URL}/${id}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch terms and conditions');
  } catch (error: any) {
    console.error('Error fetching terms and conditions by ID:', error);

    // Return mock data as fallback
    const mockData = getMockTermsConditions();
    const found = mockData.find(item => item.id === id);
    if (found) {
      return found;
    }
    throw new Error(`Terms and conditions not found with ID: ${id}`);
  }
};

// Create new terms and conditions
export const createTermsConditions = async (data: CreateTermsConditionsRequest): Promise<TermsConditions> => {
  try {
    const response = await apiService.posttermspolicies<SingleTermsConditionsResponse>(BASE_URL, data);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to create terms and conditions');
  } catch (error: any) {
    console.error('Error creating terms and conditions:', error);
    throw error;
  }
};

// Update terms and conditions
export const updateTermsConditions = async (id: number, data: UpdateTermsConditionsRequest): Promise<TermsConditions> => {
  try {
    const response = await apiService.puttermspolicies<SingleTermsConditionsResponse>(`${BASE_URL}/${id}`, data);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to update terms and conditions');
  } catch (error: any) {
    console.error('Error updating terms and conditions:', error);
    throw error;
  }
};

// Delete terms and conditions
export const deleteTermsConditions = async (id: number): Promise<void> => {
  try {
    const response = await apiService.delete<{success: boolean, message?: string}>(`${BASE_URL}/${id}`);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete terms and conditions');
    }
  } catch (error: any) {
    console.error('Error deleting terms and conditions:', error);
    throw error;
  }
};

// Search terms and conditions with filters
export const searchTermsConditions = async (filters: TermsConditionsFilters): Promise<TermsConditions[]> => {
  try {
    const params = new URLSearchParams();
    
    if (filters.search) params.append('search', filters.search);
    if (filters.language) params.append('language', filters.language);
    if (filters.language_code) params.append('language_code', filters.language_code);
    if (filters.dateRange.startDate) params.append('start_date', filters.dateRange.startDate);
    if (filters.dateRange.endDate) params.append('end_date', filters.dateRange.endDate);

    const response = await apiService.gettermspolicies<MultipleTermsConditionsResponse>(`${BASE_URL}/search?${params.toString()}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to search terms and conditions');
  } catch (error: any) {
    console.error('Error searching terms and conditions:', error);
    
    // Return filtered mock data as fallback
    return filterMockTermsConditions(filters);
  }
};

// Mock data for fallback
const getMockTermsConditions = (): TermsConditions[] => [
  {
    id: 1,
    terms: "These are the terms and conditions for English users. By using our service, you agree to comply with these terms...",
    language: "English",
    language_code: "en",
    created_at: "2025-10-09T13:13:21.584829+05:30",
    updated_at: "2025-10-09T13:13:21.584829+05:30"
  },
  {
    id: 2,
    terms: "Estos son los términos y condiciones para usuarios de español. Al usar nuestro servicio, acepta cumplir con estos términos...",
    language: "Spanish",
    language_code: "es",
    created_at: "2025-10-09T13:13:49.55117+05:30",
    updated_at: "2025-10-09T13:13:49.55117+05:30"
  },
  {
    id: 3,
    terms: "Voici les termes et conditions pour les utilisateurs francophones. En utilisant notre service, vous acceptez de vous conformer à ces termes...",
    language: "French",
    language_code: "fr",
    created_at: "2025-10-09T13:19:24.335935+05:30",
    updated_at: "2025-10-09T13:19:24.335935+05:30"
  },
  {
    id: 4,
    terms: "Dies sind die Allgemeinen Geschäftsbedingungen für deutschsprachige Benutzer. Durch die Nutzung unseres Dienstes stimmen Sie zu...",
    language: "German",
    language_code: "de",
    created_at: "2025-10-09T13:19:32.97193+05:30",
    updated_at: "2025-10-09T13:19:32.97193+05:30"
  },
  {
    id: 5,
    terms: "यह हिंदी उपयोगकर्ताओं के लिए नियम और शर्तें हैं। हमारी सेवा का उपयोग करके, आप इन शर्तों का पालन करने के लिए सहमत हैं...",
    language: "Hindi",
    language_code: "hi",
    created_at: "2025-10-09T13:23:28.315721+05:30",
    updated_at: "2025-10-09T13:23:28.315721+05:30"
  }
];

// Filter mock data based on filters
const filterMockTermsConditions = (filters: TermsConditionsFilters): TermsConditions[] => {
  let filtered = getMockTermsConditions();

  if (filters.search) {
    const searchLower = filters.search.toLowerCase();
    filtered = filtered.filter(item => 
      item.terms.toLowerCase().includes(searchLower) ||
      item.language.toLowerCase().includes(searchLower) ||
      item.language_code.toLowerCase().includes(searchLower)
    );
  }

  if (filters.language) {
    filtered = filtered.filter(item => item.language === filters.language);
  }

  if (filters.language_code) {
    filtered = filtered.filter(item => item.language_code === filters.language_code);
  }

  return filtered;
};
