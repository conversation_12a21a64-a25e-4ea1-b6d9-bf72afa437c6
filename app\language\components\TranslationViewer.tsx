'use client';

import { useState } from 'react';

interface TranslationViewerProps {
  data: Record<string, any>;
  title?: string;
}

interface TreeNode {
  key: string;
  value: any;
  path: string[];
  isLeaf: boolean;
  children?: TreeNode[];
}

export default function TranslationViewer({ data, title = "Translation Data" }: TranslationViewerProps) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'tree' | 'json'>('tree');

  // Build tree structure from nested object
  const buildTree = (obj: Record<string, any>, path: string[] = []): TreeNode[] => {
    return Object.entries(obj).map(([key, value]) => {
      const currentPath = [...path, key];
      const pathString = currentPath.join('.');
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        return {
          key,
          value,
          path: currentPath,
          isLeaf: false,
          children: buildTree(value, currentPath)
        };
      } else {
        return {
          key,
          value,
          path: currentPath,
          isLeaf: true
        };
      }
    });
  };

  // Toggle node expansion
  const toggleNode = (pathString: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(pathString)) {
      newExpanded.delete(pathString);
    } else {
      newExpanded.add(pathString);
    }
    setExpandedNodes(newExpanded);
  };

  // Expand all nodes
  const expandAll = () => {
    const allPaths = new Set<string>();
    
    const collectPaths = (nodes: TreeNode[]) => {
      nodes.forEach(node => {
        if (!node.isLeaf) {
          allPaths.add(node.path.join('.'));
          if (node.children) {
            collectPaths(node.children);
          }
        }
      });
    };
    
    collectPaths(tree);
    setExpandedNodes(allPaths);
  };

  // Collapse all nodes
  const collapseAll = () => {
    setExpandedNodes(new Set());
  };

  // Filter tree based on search term
  const filterTree = (nodes: TreeNode[], searchTerm: string): TreeNode[] => {
    if (!searchTerm) return nodes;
    
    const searchLower = searchTerm.toLowerCase();
    
    return nodes.filter(node => {
      if (node.isLeaf) {
        return (
          node.key.toLowerCase().includes(searchLower) ||
          String(node.value).toLowerCase().includes(searchLower) ||
          node.path.join('.').toLowerCase().includes(searchLower)
        );
      } else {
        // For non-leaf nodes, check if any children match
        const filteredChildren = node.children ? filterTree(node.children, searchTerm) : [];
        return (
          node.key.toLowerCase().includes(searchLower) ||
          node.path.join('.').toLowerCase().includes(searchLower) ||
          filteredChildren.length > 0
        );
      }
    }).map(node => {
      if (!node.isLeaf && node.children) {
        return {
          ...node,
          children: filterTree(node.children, searchTerm)
        };
      }
      return node;
    });
  };

  // Render tree node
  const renderTreeNode = (node: TreeNode, level: number = 0) => {
    const pathString = node.path.join('.');
    const isExpanded = expandedNodes.has(pathString);
    const hasChildren = !node.isLeaf && node.children && node.children.length > 0;

    return (
      <div key={pathString} className="select-text">
        <div 
          className={`flex items-center py-2 px-3 hover:bg-gray-50 rounded ${
            level > 0 ? 'ml-' + (level * 4) : ''
          }`}
          style={{ marginLeft: `${level * 1.5}rem` }}
        >
          {/* Expand/Collapse Button */}
          {hasChildren ? (
            <button
              onClick={() => toggleNode(pathString)}
              className="mr-2 p-1 hover:bg-gray-200 rounded"
            >
              <i className={`ri-arrow-${isExpanded ? 'down' : 'right'}-s-line text-gray-500 text-sm`}></i>
            </button>
          ) : (
            <div className="w-6 mr-2"></div>
          )}

          {/* Node Icon */}
          <div className="mr-3">
            {node.isLeaf ? (
              <i className="ri-file-text-line text-blue-500 text-sm"></i>
            ) : (
              <i className={`ri-folder-${isExpanded ? 'open' : ''}-line text-yellow-500 text-sm`}></i>
            )}
          </div>

          {/* Node Content */}
          <div className="flex-1 min-w-0">
            {node.isLeaf ? (
              <div className="flex items-center space-x-3">
                <span className="font-mono text-sm text-gray-700 font-medium">{node.key}:</span>
                <span className="text-sm text-gray-900 bg-gray-100 px-2 py-1 rounded">
                  "{String(node.value)}"
                </span>
                <button
                  onClick={() => navigator.clipboard.writeText(String(node.value))}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-200"
                  title="Copy value"
                >
                  <i className="ri-file-copy-line text-xs"></i>
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <span className="font-mono text-sm font-semibold text-gray-800">{node.key}</span>
                <span className="text-xs text-gray-500">
                  ({node.children?.length || 0} items)
                </span>
                <button
                  onClick={() => navigator.clipboard.writeText(pathString)}
                  className="p-1 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-200"
                  title="Copy path"
                >
                  <i className="ri-file-copy-line text-xs"></i>
                </button>
              </div>
            )}
          </div>

          {/* Path Badge */}
          <div className="text-xs text-gray-400 font-mono">
            {node.path.join('.')}
          </div>
        </div>

        {/* Children */}
        {hasChildren && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const tree = buildTree(data);
  const filteredTree = filterTree(tree, searchTerm);

  // Count total translations
  const countTranslations = (nodes: TreeNode[]): number => {
    return nodes.reduce((count, node) => {
      if (node.isLeaf) {
        return count + 1;
      } else if (node.children) {
        return count + countTranslations(node.children);
      }
      return count;
    }, 0);
  };

  const totalTranslations = countTranslations(tree);

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        <div className="flex items-center space-x-2">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('tree')}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === 'tree' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <i className="ri-node-tree mr-1"></i>
              Tree
            </button>
            <button
              onClick={() => setViewMode('json')}
              className={`px-3 py-1 text-sm rounded ${
                viewMode === 'json' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <i className="ri-code-line mr-1"></i>
              JSON
            </button>
          </div>
        </div>
      </div>

      {viewMode === 'tree' && (
        <>
          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                <input
                  type="text"
                  placeholder="Search translations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={expandAll}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
              >
                Expand All
              </button>
              <button
                onClick={collapseAll}
                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
              >
                Collapse All
              </button>
            </div>
          </div>

          {/* Tree View */}
          <div className="border border-gray-200 rounded-lg bg-white">
            {filteredTree.length > 0 ? (
              <div className="p-4">
                {filteredTree.map(node => renderTreeNode(node))}
              </div>
            ) : (
              <div className="p-8 text-center text-gray-500">
                <i className="ri-search-line text-4xl mb-4 block"></i>
                <p className="text-lg font-medium">No translations found</p>
                <p className="text-sm">Try adjusting your search terms</p>
              </div>
            )}
          </div>
        </>
      )}

      {viewMode === 'json' && (
        <div className="border border-gray-200 rounded-lg bg-white">
          <div className="p-4">
            <pre className="text-sm font-mono text-gray-800 whitespace-pre-wrap overflow-x-auto">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div className="flex items-center space-x-2 text-purple-800">
          <i className="ri-information-line"></i>
          <span className="text-sm font-medium">
            Total: {totalTranslations} translations across {Object.keys(data).length} sections
          </span>
        </div>
      </div>
    </div>
  );
}
