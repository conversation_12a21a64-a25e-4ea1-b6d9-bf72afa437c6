'use client';

import { useState } from 'react';
import { Language, LanguageUtils } from '../language.model';
import Pagination from '../../styles/components/Pagination';

interface LanguageListProps {
  languages: Language[];
  onEdit: (language: Language) => void;
  onView: (language: Language) => void;
  onDelete: (language: Language) => void;
  onToggleStatus: (language: Language) => void;
  loading?: boolean;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

export default function LanguageList({
  languages = [],
  onEdit,
  onView,
  onDelete,
  onToggleStatus,
  loading = false,
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange
}: LanguageListProps) {
  const [sortField, setSortField] = useState<keyof Language>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Handle sorting
  const handleSort = (field: keyof Language) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort languages
  const sortedLanguages = [...languages].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    return 0;
  });

  const SortIcon = ({ field }: { field: keyof Language }) => {
    if (sortField !== field) {
      return <i className="ri-arrow-up-down-line text-gray-400 ml-1"></i>;
    }
    return sortDirection === 'asc' 
      ? <i className="ri-arrow-up-line text-blue-600 ml-1"></i>
      : <i className="ri-arrow-down-line text-blue-600 ml-1"></i>;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Table Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Languages</h3>
          <div className="text-sm text-gray-500">
            Showing {languages.length} of {totalItems} languages
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  Language Name
                  <SortIcon field="name" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('language_code')}
              >
                <div className="flex items-center">
                  Language Code
                  <SortIcon field="language_code" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('country_code')}
              >
                <div className="flex items-center">
                  Country Code
                  <SortIcon field="country_code" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('CreatedAt')}
              >
                <div className="flex items-center">
                  Created At
                  <SortIcon field="CreatedAt" />
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('UpdatedAt')}
              >
                <div className="flex items-center">
                  Updated At
                  <SortIcon field="UpdatedAt" />
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span className="ml-3 text-gray-500">Loading languages...</span>
                  </div>
                </td>
              </tr>
            ) : sortedLanguages.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="text-gray-500">
                    <i className="ri-translate-line text-4xl mb-4 block"></i>
                    <p className="text-lg font-medium">No languages found</p>
                    <p className="text-sm">Try adjusting your search filters</p>
                  </div>
                </td>
              </tr>
            ) : (
              sortedLanguages.map((language) => (
                <tr key={language.ID} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-lg bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
                          <i className="ri-translate-line text-purple-700 text-lg"></i>
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{language.name}</div>
                        <div className="text-sm text-gray-500">
                          {language.json?.native_name || 'Native name not available'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 uppercase">
                      {language.language_code}
                    </div>
                    <div className="text-sm text-gray-500">ISO 639</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 uppercase">
                      {language.country_code}
                    </div>
                    <div className="text-sm text-gray-500">
                      {language.json?.region || 'Region not specified'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {LanguageUtils.formatDate(language.CreatedAt)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {LanguageUtils.formatDate(language.UpdatedAt)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => onToggleStatus(language)}
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        language.is_active
                          ? 'bg-green-100 text-green-800 hover:bg-green-200'
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      } transition-colors`}
                    >
                      <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                        language.is_active ? 'bg-green-400' : 'bg-red-400'
                      }`}></span>
                      {language.is_active ? 'Active' : 'Inactive'}
                    </button>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onView(language)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                        title="View Details"
                      >
                        <i className="ri-eye-line"></i>
                      </button>
                      <button
                        onClick={() => onEdit(language)}
                        className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                        title="Edit Language"
                      >
                        <i className="ri-edit-line"></i>
                      </button>
                      <button
                        onClick={() => onDelete(language)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Delete Language"
                      >
                        <i className="ri-delete-bin-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {languages && languages.length > 0 && (
        <div className="mt-6 px-6 pb-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={pageSize}
            totalItems={totalItems}
            onPageChange={onPageChange}
            onItemsPerPageChange={onPageSizeChange}
          />
        </div>
      )}
    </div>
  );
}
