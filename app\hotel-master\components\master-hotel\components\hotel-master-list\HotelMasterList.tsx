import React, { useCallback, useEffect, useState } from 'react'
import HotelMasterFilter, { hotelMasterFilterData } from './components/HotelMasterFilter'
import { Hotel, hotelDetail } from '@/app/hotel-master/hotel-master.model';
import Pagination from '@/app/styles/components/Pagination';
import { getHotelDetailsApi, getHotelListApi } from '@/app/hotel-master/hotel-master-service';
import HotelMasterView from './components/HotelMasterView';
import MasterHotelAddEdit from './components/HotelMasterAddEdit';
import Modal from '@/app/components/ui/Modal';

interface prop{
    onTotalItemsChange: (totalItems: number) => void;
}

 function HotelMasterList({onTotalItemsChange}:prop) {
    const [sortField, setSortField] = useState<keyof Hotel>('updated_at');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
    const [isLoading,setIsloading] = useState<boolean>(true);
    const [isDetailLoading,setIsDetailLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [hotels, setHotels] = useState<Hotel[]>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [itemsPerPage, setItemsPerPage] = useState<number>(10);
    const [totalPages, setTotalPages] = useState<number>(1);
    const [totalItems, setTotalItems] = useState<number>(0);
    const [isViewHotel, setIsViewHotel] = useState<boolean>(false);
    const [isEditHotel, setIsEditHotel] = useState<boolean>(false);
    const [selectedHotel, setSelectedHotel] = useState<hotelDetail | null>(null);

    const [filters, setFilters] = React.useState<hotelMasterFilterData>({
        search: '',
        status: '',
        country: '',
        starRating: '',
        contractType: '',
        provider: '',
        country_code: '',
        provider_id: '',
        provider_hotel_id: '',
        has_phone: false,
        has_attributes: false,
    })

    const getHotelDetails = useCallback(async ( selectedHotelId: string)=>{
        setIsDetailLoading(true);
        try{
            const response = await getHotelDetailsApi(selectedHotelId);
            if(response && response.id){
                setSelectedHotel(response);
            }
        }catch(error){
            console.log(error);
        }finally{
            setIsDetailLoading(false);
        }
    },[])

    const handleFiltersChange = (newFilters: hotelMasterFilterData) => {
        setFilters(newFilters);
    };

    const onViewHotelDetails = (hotelId: string) => {
        if(!(selectedHotel && selectedHotel.hotel_id === hotelId)){
            getHotelDetails(hotelId);
        }
        setIsViewHotel(true);
    }

    const onEditHotel = (hotelId: string) => {
        if(!(selectedHotel && selectedHotel.hotel_id === hotelId)){
            getHotelDetails(hotelId);
        }
        setIsEditHotel(true);
    }


    const getHotelList = useCallback( async(page:number, itemsPerPageNo:number, filters:hotelMasterFilterData) => {

        setIsloading(true);
        setError(null);
        const skip = (page - 1) * itemsPerPageNo;
        const limit = itemsPerPageNo;
        try{
            const response = await getHotelListApi(skip, limit, filters);
            if(response && response.items){
                setHotels(response.items);
                setTotalPages(response.pagination.total_pages);
                setTotalItems(response.pagination.total_items);
                setCurrentPage(response.pagination.current_page);
                onTotalItemsChange(response.pagination.total_items);
            } else {
                setHotels([]);
                setTotalPages(1);
                setTotalItems(0);
                setCurrentPage(1);
                onTotalItemsChange(0);
            }
        }catch(error){
            console.error('Error fetching hotels:', error);
            setError('Failed to load hotels. Please try again.');
            setHotels([]);
            setTotalPages(1);
            setTotalItems(0);
            setCurrentPage(1);
            onTotalItemsChange(0);
        }finally{
            setIsloading(false);
        }
    },[onTotalItemsChange])


    const applayFilter = () => {
        setCurrentPage(1); // Reset to first page when applying filters
        getHotelList(1, itemsPerPage, filters);
    }

    const clearFilter = () => {
        const clearedFilters = {
            search: '',
            status: '',
            country: '',
            starRating: '',
            contractType: '',
            provider: '',
            country_code: '',
            provider_id: '',
            provider_hotel_id: '',
            has_phone: false,
            has_attributes: false,
        };
        setFilters(clearedFilters);
        setCurrentPage(1); // Reset to first page when clearing filters
        getHotelList(1, itemsPerPage, clearedFilters);
    }

    const handleSaveHotel = () => {
        setIsEditHotel(false);
    }

    const handlePageChange = (newPage: number) => {
        setCurrentPage(newPage);
        getHotelList(newPage, itemsPerPage, filters);
    };

    const handleItemsPerPageChange = (newItemsPerPage: number) => {
        setItemsPerPage(newItemsPerPage);
        setCurrentPage(1); // Reset to first page when changing items per page
        getHotelList(1, newItemsPerPage, filters);
    };

    const handleSort = (field: keyof Hotel) => {
        if (sortField === field) {
          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
          setSortField(field);
          setSortDirection('asc');
        }
    };

    const getStatusBadge = (hotel: Hotel) => {
        // Since API doesn't return status, we'll determine it based on available data
        let status = 'active';
        let statusText = 'Active';

        // If hotel has no phone and no attributes, consider it incomplete/pending
        if ((!hotel.phones_json || hotel.phones_json.length === 0) &&
            (!hotel.attributes_json || hotel.attributes_json.length === 0)) {
            status = 'pending';
            statusText = 'Incomplete';
        }
        // If hotel has no rating, consider it pending
        else if (!hotel.rating_score || parseFloat(hotel.rating_score) === 0) {
            status = 'pending';
            statusText = 'Pending Review';
        }

        const statusConfig = {
          active: 'bg-green-100 text-green-800',
          inactive: 'bg-red-100 text-red-800',
          pending: 'bg-yellow-100 text-yellow-800'
        };

        return (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${statusConfig[status as keyof typeof statusConfig] || 'bg-gray-100 text-gray-800'}`}>
            {statusText}
          </span>
        );
    };

    useEffect(() => {
        getHotelList(1, itemsPerPage, filters);
    }, [getHotelList, itemsPerPage]);



  return (
    <div className='w-full'>
        <HotelMasterFilter filters={filters} onFiltersChange={handleFiltersChange} onApplyFilters={applayFilter} onClearFilters={clearFilter} />

        {/* Error Display */}
        {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex items-center">
                    <i className="ri-error-warning-line text-red-500 mr-2"></i>
                    <span className="text-red-700">{error}</span>
                    <button
                        onClick={() => getHotelList(currentPage, itemsPerPage, filters)}
                        className="ml-auto text-red-600 hover:text-red-800 underline"
                    >
                        Retry
                    </button>
                </div>
            </div>
        )}

        <div className="list-container">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                {(isLoading || hotels.length > 0) ? (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th 
                                        scope="col" 
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('name')}
                                    >
                                        <div className="flex items-center">
                                            Hotel Name
                                            {sortField === 'name' && (
                                                <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                                            )}
                                        </div>
                                    </th>
                                    <th 
                                        scope="col" 
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('country')}
                                    >
                                        <div className="flex items-center">
                                            Location
                                            {sortField === 'country' && (
                                                <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                                            )}
                                        </div>
                                    </th>
                                    <th 
                                        scope="col" 
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('rating_score')}
                                    >
                                        <div className="flex items-center">
                                            Rating
                                            {sortField === 'rating_score' && (
                                                <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                                            )}
                                        </div>
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Type & ID
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Contact
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Status
                                    </th>
                                    <th
                                        scope="col"
                                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                        onClick={() => handleSort('updated_at')}
                                    >
                                        <div className="flex items-center">
                                            Last Updated
                                            {sortField === 'updated_at' && (
                                                <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                                            )}
                                        </div>
                                    </th>
                                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {isLoading ? (
                                    // Loading state
                                    Array.from({ length: 5 }).map((_, index) => (
                                        <tr key={`loading-${index}`} className="animate-pulse">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-lg"></div>
                                                    <div className="ml-4">
                                                        <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
                                                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                                                <div className="h-3 bg-gray-200 rounded w-16"></div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                                                <div className="h-3 bg-gray-200 rounded w-16"></div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="h-6 bg-gray-200 rounded-full w-16"></div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="h-4 bg-gray-200 rounded w-20"></div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="h-4 bg-gray-200 rounded w-24"></div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                                                    <div className="w-8 h-8 bg-gray-200 rounded-lg"></div>
                                                </div>
                                            </td>
                                        </tr>
                                    ))
                                ) : (
                                    hotels.map((hotel) => (
                                        <tr key={hotel.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <img
                                                            className="h-10 w-10 rounded-lg object-cover"
                                                            src={`https://readdy.ai/api/search-image?query=luxury%20hotel%20exterior%20architecture%20modern%20building%20facade&width=40&height=40&seq=${hotel.id}&orientation=squarish`}
                                                            alt={hotel.name}
                                                        />
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">{hotel.name}</div>
                                                        <div className="text-sm text-gray-500 flex items-center space-x-2">
                                                            <span>{hotel.type || 'N/A'}</span>
                                                            {hotel.provider_name && (
                                                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                                                    {hotel.provider_name}
                                                                </span>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {hotel.city && hotel.country ? `${hotel.city}, ${hotel.country}` : hotel.country || hotel.city || 'N/A'}
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    {hotel.address || 'Address not available'}
                                                </div>
                                                {hotel.country_code && (
                                                    <div className="text-xs text-gray-400 mt-1 flex items-center">
                                                        <i className="ri-map-pin-line mr-1"></i>
                                                        {hotel.country_code}
                                                    </div>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center space-x-1">
                                                    {(() => {
                                                        const rating = parseFloat(hotel.rating_score) || 0;
                                                        const fullStars = Math.floor(rating);
                                                        const hasHalfStar = rating % 1 >= 0.5;
                                                        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

                                                        return (
                                                            <>
                                                                {/* Full stars */}
                                                                {[...Array(fullStars)].map((_, i) => (
                                                                    <svg key={`full-${i}`} className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                    </svg>
                                                                ))}
                                                                {/* Half star */}
                                                                {hasHalfStar && (
                                                                    <svg className="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                                        <defs>
                                                                            <linearGradient id={`half-${hotel.id}`}>
                                                                                <stop offset="50%" stopColor="currentColor" />
                                                                                <stop offset="50%" stopColor="#d1d5db" />
                                                                            </linearGradient>
                                                                        </defs>
                                                                        <path fill={`url(#half-${hotel.id})`} d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                    </svg>
                                                                )}
                                                                {/* Empty stars */}
                                                                {[...Array(emptyStars)].map((_, i) => (
                                                                    <svg key={`empty-${i}`} className="w-4 h-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                                                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                                                    </svg>
                                                                ))}
                                                            </>
                                                        );
                                                    })()}
                                                    <span className="text-sm text-gray-500 ml-2">
                                                        {parseFloat(hotel.rating_score).toFixed(1)}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">{hotel.type || 'N/A'}</div>
                                                <div className="text-sm text-gray-500 space-y-1">
                                                    {/* {hotel.contractType && (
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                                            hotel.contractType === 'static' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                                                        }`}>
                                                            {hotel.contractType}
                                                        </span>
                                                    )} */}
                                                    {hotel.hotel_id && (
                                                        <div className="text-xs text-gray-400">
                                                            ID: {hotel.hotel_id}
                                                        </div>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    {hotel.phones_json && hotel.phones_json.length > 0 ? (
                                                        <div className="flex items-center">
                                                            <i className="ri-phone-line text-gray-400 mr-1"></i>
                                                            <span>{hotel.phones_json[0]}</span>
                                                        </div>
                                                    ) : (
                                                        <span className="text-gray-400">No phone</span>
                                                    )}
                                                </div>
                                                {hotel.faxes_json && hotel.faxes_json.length > 0 && (
                                                    <div className="text-xs text-gray-500 flex items-center mt-1">
                                                        <i className="ri-printer-line text-gray-400 mr-1"></i>
                                                        <span>Fax available</span>
                                                    </div>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {getStatusBadge(hotel)}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {hotel.updated_at ? new Date(hotel.updated_at).toLocaleDateString('en-US', {
                                                    year: 'numeric',
                                                    month: 'short',
                                                    day: 'numeric'
                                                }) : (
                                                    hotel.created_at ? new Date(hotel.created_at).toLocaleDateString('en-US', {
                                                        year: 'numeric',
                                                        month: 'short',
                                                        day: 'numeric'
                                                    }) : 'N/A'
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center justify-end space-x-2">
                                                    <button
                                                        onClick={() => onViewHotelDetails(hotel.hotel_id)}
                                                        className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                                                        title="View Details"
                                                    >
                                                        <i className="ri-eye-line"></i>
                                                    </button>
                                                    <button
                                                        onClick={() => onEditHotel(hotel.hotel_id)}
                                                        className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                                                        title="Edit Hotel"
                                                    >
                                                        <i className="ri-edit-line"></i>
                                                    </button>
                                                    <button
                                                        className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                                                        title="Delete Hotel"
                                                    >
                                                        <i className="ri-delete-bin-line"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>
                ) : (
                    <div className="text-center py-12">
                        <i className="ri-hotel-line text-gray-300 text-4xl mb-4"></i>
                        <h3 className="text-lg font-medium text-gray-900 mb-2">No hotels found</h3>
                        <p className="text-gray-500">Get started by adding your first hotel listing.</p>
                    </div>
                )}

                {/* Pagination */}
                {hotels && hotels.length > 0 && (
                    <div className="mt-6">
                        <Pagination
                            currentPage={currentPage}
                            totalPages={totalPages}
                            itemsPerPage={itemsPerPage}
                            totalItems={totalItems}
                            onPageChange={handlePageChange}
                            onItemsPerPageChange={handleItemsPerPageChange}
                        />
                    </div>
                )}
            </div>

        </div>

        {/* modals */}
        {isViewHotel && selectedHotel && (
            <HotelMasterView
                hotel={selectedHotel}
                isOpen={isViewHotel}
                onClose={() => setIsViewHotel(false)}
                onEdit={onEditHotel}
                loading={isDetailLoading}
            />
        )}
        {isEditHotel && selectedHotel && (
            <Modal isOpen={isEditHotel} onClose={() => setIsEditHotel(false)} title="Edit Hotel" size="xl" height="fixed">
                <MasterHotelAddEdit
                    hotel={selectedHotel}
                    isOpen={isEditHotel}
                    onSave={handleSaveHotel}
                    onClose={() => setIsEditHotel(false)}
                />
            </Modal>
        )}
    </div>
  )
}

export default HotelMasterList