// Language Management Models

// Main Language interface based on API response
export interface Language {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  name: string;
  country_code: string;
  language_code: string;
  json: {
    native_name?: string;
    region?: string;
    [key: string]: any;
  };
  is_active: boolean;
}

// API Response interface
export interface LanguageResponse {
  success: boolean;
  data: Language[];
  total: number;
}

// Request interfaces for API operations
export interface CreateLanguageRequest {
  name: string;
  country_code: string;
  language_code: string;
  json?: {
    native_name?: string;
    region?: string;
    [key: string]: any;
  };
  is_active: boolean;
}

export interface UpdateLanguageRequest {
  name?: string;
  country_code?: string;
  language_code?: string;
  json?: {
    native_name?: string;
    region?: string;
    [key: string]: any;
  };
  is_active?: boolean;
}

// Filter interface for language search
export interface LanguageFilters {
  search?: string;
  country_code?: string;
  language_code?: string;
  is_active?: boolean;
}

// Pagination interface
export interface LanguagePaginationParams {
  page: number;
  pageSize: number;
  sortField?: keyof Language;
  sortDirection?: 'asc' | 'desc';
  filters?: LanguageFilters;
}

// Common country codes for validation
export const CommonCountryCodes = [
  'US', 'GB', 'CA', 'AU', 'DE', 'FR', 'ES', 'IT', 'JP', 'KR',
  'CN', 'IN', 'BR', 'MX', 'RU', 'SA', 'AE', 'SG', 'MY', 'TH',
  'ID', 'VN', 'PH', 'TR', 'EG', 'ZA', 'NG', 'KE', 'GH', 'MA'
];

// Common language codes (ISO 639-1)
export const CommonLanguageCodes = [
  'en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh',
  'ar', 'hi', 'th', 'vi', 'id', 'ms', 'tl', 'tr', 'pl', 'nl',
  'sv', 'da', 'no', 'fi', 'cs', 'sk', 'hu', 'ro', 'bg', 'hr',
  'sl', 'et', 'lv', 'lt', 'mt', 'cy', 'is', 'mk', 'sq', 'sr',
  'mal', 'ta', 'te', 'kn', 'ml', 'gu', 'pa', 'bn', 'or', 'as'
];

// Language utility functions
export const LanguageUtils = {
  // Format date for display
  formatDate: (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },

  // Get language by code
  getLanguageByCode: (languages: Language[], code: string): Language | undefined => {
    return languages.find(language => language.language_code === code);
  },

  // Validate language code format (ISO 639-1 or 639-3)
  isValidLanguageCode: (code: string): boolean => {
    return /^[a-z]{2,3}$/.test(code.toLowerCase());
  },

  // Validate country code format (ISO 3166-1 alpha-2)
  isValidCountryCode: (code: string): boolean => {
    return /^[A-Z]{2}$/.test(code.toUpperCase());
  },

  // Sort languages by name
  sortByName: (languages: Language[]): Language[] => {
    return [...languages].sort((a, b) => a.name.localeCompare(b.name));
  },

  // Sort languages by code
  sortByCode: (languages: Language[]): Language[] => {
    return [...languages].sort((a, b) => a.language_code.localeCompare(b.language_code));
  },

  // Filter active languages
  getActiveLanguages: (languages: Language[]): Language[] => {
    return languages.filter(language => language.is_active);
  },

  // Get languages by country
  getLanguagesByCountry: (languages: Language[], countryCode: string): Language[] => {
    return languages.filter(language => language.country_code === countryCode);
  }
};

// Form validation rules
export const LanguageValidation = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    pattern: /^[a-zA-Z\s\-']+$/
  },
  country_code: {
    required: true,
    length: 2,
    pattern: /^[A-Z]{2}$/
  },
  language_code: {
    required: true,
    minLength: 2,
    maxLength: 3,
    pattern: /^[a-z]{2,3}$/
  }
};

export default Language;
