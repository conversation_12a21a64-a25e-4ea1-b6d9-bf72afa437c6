'use client';

export default function TestLayoutPage() {
  return (
    <div className="p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 mb-2">Layout Test Page</h1>
          <p className="text-slate-600">Testing the fixed header and sidebar with scrollable content.</p>
        </div>
        
        {/* Content to test scrolling */}
        <div className="space-y-6">
          {Array.from({ length: 20 }, (_, i) => (
            <div key={i} className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-semibold mb-2">Content Block {i + 1}</h3>
              <p className="text-gray-600 mb-4">
                This is a test content block to demonstrate the scrollable content area. 
                The header and sidebar should remain fixed while this content scrolls.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 p-4 rounded">
                  <h4 className="font-medium text-blue-900">Feature 1</h4>
                  <p className="text-blue-700 text-sm">Fixed header at top</p>
                </div>
                <div className="bg-green-50 p-4 rounded">
                  <h4 className="font-medium text-green-900">Feature 2</h4>
                  <p className="text-green-700 text-sm">Fixed sidebar on left</p>
                </div>
                <div className="bg-purple-50 p-4 rounded">
                  <h4 className="font-medium text-purple-900">Feature 3</h4>
                  <p className="text-purple-700 text-sm">Scrollable content area</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-900 mb-2">Layout Verification</h3>
          <ul className="text-yellow-800 space-y-1">
            <li>✓ Header should stay fixed at the top while scrolling</li>
            <li>✓ Sidebar should stay fixed on the left while scrolling</li>
            <li>✓ Content should scroll independently</li>
            <li>✓ Sidebar should be collapsible</li>
            <li>✓ Layout should be responsive on mobile</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
