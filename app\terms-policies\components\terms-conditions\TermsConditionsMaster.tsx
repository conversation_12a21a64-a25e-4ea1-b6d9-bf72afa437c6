'use client';

import { useState, useEffect } from 'react';
import TermsConditionsList from './TermsConditionsList';
import TermsConditionsAddEdit from './TermsConditionsAddEdit';
import TermsConditionsView from './TermsConditionsView';
import Modal from '../../../components/ui/Modal';
import { 
  TermsConditions, 
  TermsConditionsFormData, 
  TermsConditionsFilters,
  getDefaultTermsConditionsFormData,
  LANGUAGE_OPTIONS
} from '../../terms-conditions.model';
import {
  getAllTermsConditions,
  createTermsConditions,
  updateTermsConditions,
  deleteTermsConditions,
  searchTermsConditions
} from '../../terms-conditions.service';

export default function TermsConditionsMaster() {
  const [termsConditions, setTermsConditions] = useState<TermsConditions[]>([]);
  const [selectedTermsConditions, setSelectedTermsConditions] = useState<TermsConditions | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<TermsConditionsFilters>({
    search: '',
    language: '',
    language_code: '',
    dateRange: {
      startDate: '',
      endDate: ''
    }
  });

  // Load terms and conditions
  const loadTermsConditions = async () => {
    try {
      setLoading(true);
      const data = await getAllTermsConditions();
      setTermsConditions(data);
    } catch (error) {
      console.error('Error loading terms and conditions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Search terms and conditions
  const handleSearch = async () => {
    try {
      setLoading(true);
      const data = await searchTermsConditions(filters);
      setTermsConditions(data);
    } catch (error) {
      console.error('Error searching terms and conditions:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadTermsConditions();
  }, []);

  // Handle search when filters change
  useEffect(() => {
    if (filters.search || filters.language || filters.language_code || filters.dateRange.startDate || filters.dateRange.endDate) {
      handleSearch();
    } else {
      loadTermsConditions();
    }
  }, [filters]);

  // Filter terms and conditions locally
  const filteredTermsConditions = termsConditions.filter(item => {
    const matchesSearch = !filters.search || 
      item.terms.toLowerCase().includes(filters.search.toLowerCase()) ||
      item.language.toLowerCase().includes(filters.search.toLowerCase()) ||
      item.language_code.toLowerCase().includes(filters.search.toLowerCase());
    
    const matchesLanguage = !filters.language || item.language === filters.language;
    const matchesLanguageCode = !filters.language_code || item.language_code === filters.language_code;

    return matchesSearch && matchesLanguage && matchesLanguageCode;
  });

  // Handle add new
  const handleAddNew = () => {
    setSelectedTermsConditions(null);
    setFormMode('add');
    setIsFormOpen(true);
  };

  // Handle edit
  const handleEdit = (termsConditions: TermsConditions) => {
    setSelectedTermsConditions(termsConditions);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  // Handle view
  const handleView = (termsConditions: TermsConditions) => {
    setSelectedTermsConditions(termsConditions);
    setIsViewOpen(true);
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this terms and conditions?')) {
      try {
        await deleteTermsConditions(id);
        await loadTermsConditions();
      } catch (error) {
        console.error('Error deleting terms and conditions:', error);
        alert('Failed to delete terms and conditions');
      }
    }
  };

  // Handle form save
  const handleFormSave = async (formData: TermsConditionsFormData) => {
    try {
      if (formMode === 'add') {
        await createTermsConditions(formData);
      } else if (selectedTermsConditions) {
        await updateTermsConditions(selectedTermsConditions.id, formData);
      }
      
      setIsFormOpen(false);
      setSelectedTermsConditions(null);
      await loadTermsConditions();
    } catch (error) {
      console.error('Error saving terms and conditions:', error);
      throw error;
    }
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedTermsConditions(null);
  };

  // Handle view close
  const handleViewClose = () => {
    setIsViewOpen(false);
    setSelectedTermsConditions(null);
  };

  // Handle view edit
  const handleViewEdit = (termsConditions: TermsConditions) => {
    setIsViewOpen(false);
    handleEdit(termsConditions);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-gray-200 rounded-lg h-96"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Terms & Conditions</h2>
          <p className="text-gray-600">Manage terms and conditions for different languages</p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={loadTermsConditions}
            className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors shadow-sm"
          >
            <i className="ri-refresh-line mr-2"></i>
            Refresh
          </button>
          <button 
            onClick={handleAddNew}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
          >
            <i className="ri-add-line mr-2"></i>
            Add Terms & Conditions
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search terms, language..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select
              value={filters.language}
              onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Languages</option>
              {LANGUAGE_OPTIONS.map(lang => (
                <option key={lang.code} value={lang.value}>{lang.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language Code</label>
            <select
              value={filters.language_code}
              onChange={(e) => setFilters(prev => ({ ...prev, language_code: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Codes</option>
              {LANGUAGE_OPTIONS.map(lang => (
                <option key={lang.code} value={lang.code}>{lang.code.toUpperCase()}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Terms and Conditions List */}
      <TermsConditionsList
        termsConditions={filteredTermsConditions}
        onEdit={handleEdit}
        onView={handleView}
        onDelete={handleDelete}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isFormOpen}
        onClose={handleFormCancel}
        title={formMode === 'add' ? 'Add New Terms & Conditions' : 'Edit Terms & Conditions'}
        size="full"
        height="fixed"
      >
        <TermsConditionsAddEdit
          termsConditions={selectedTermsConditions}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
          mode={formMode}
        />
      </Modal>

      {/* View Modal */}
      <TermsConditionsView
        termsConditions={selectedTermsConditions}
        isOpen={isViewOpen}
        onClose={handleViewClose}
        onEdit={handleViewEdit}
      />
    </div>
  );
}
