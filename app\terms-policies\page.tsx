'use client';

import { useState } from 'react';
import TermsConditionsMaster from './components/terms-conditions/TermsConditionsMaster';
import PrivacyPoliciesMaster from './components/privacy-policies/PrivacyPoliciesMaster';

export default function TermsPoliciesPage() {
  const [lastSyncTime] = useState(new Date().toLocaleTimeString());
  const [activeTab, setActiveTab] = useState<'terms-conditions' | 'privacy-policies'>('terms-conditions');

  const tabs = [
    {
      id: 'terms-conditions' as const,
      label: 'Terms & Conditions',
      icon: 'ri-file-text-line',
      description: 'Manage terms and conditions for different languages'
    },
    {
      id: 'privacy-policies' as const,
      label: 'Privacy Policies',
      icon: 'ri-shield-user-line',
      description: 'Manage privacy policies for different languages'
    }
  ];

  return (
    <div className="h-screen flex flex-col overflow-hidden">
      {/* Header Section */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Terms & Policies</h1>
              <p className="text-gray-600">Manage legal documents and policies for your platform</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-500">
                Last sync: {lastSyncTime}
              </div>
            </div>
          </div>

          
         

          {/* Tab Navigation */}
          <nav className="flex space-x-1 bg-gray-100 rounded-lg p-1">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 flex items-center justify-center px-4 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-center">
                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${
                    activeTab === tab.id ? 'text-blue-600' : 'text-gray-600'
                  }`}>
                    <i className={`${tab.icon} text-base ${
                      activeTab === tab.id ? 'text-blue-600' : 'text-gray-600'
                    }`}></i>
                  </div>
                  <div className="text-left">
                    <div className="font-semibold">{tab.label}</div>
                    <div className="text-xs font-normal opacity-75 hidden sm:block">{tab.description}</div>
                  </div>
                </div>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content Area with Internal Scrolling */}
      <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
        <div className="p-6">
          <div className="max-w-full">
            {activeTab === 'terms-conditions' && (
              <div className="animate-fade-in">
                <TermsConditionsMaster />
              </div>
            )}

            {activeTab === 'privacy-policies' && (
              <div className="animate-fade-in">
                <PrivacyPoliciesMaster />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
