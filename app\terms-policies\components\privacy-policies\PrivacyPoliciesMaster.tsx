'use client';

import { useState, useEffect } from 'react';
import PrivacyPoliciesList from './PrivacyPoliciesList';
import PrivacyPoliciesAddEdit from './PrivacyPoliciesAddEdit';
import PrivacyPoliciesView from './PrivacyPoliciesView';
import Modal from '../../../components/ui/Modal';
import { 
  PrivacyPolicies, 
  PrivacyPoliciesFormData, 
  PrivacyPoliciesFilters,
  getDefaultPrivacyPoliciesFormData,
  LANGUAGE_OPTIONS
} from '../../privacy-policies.model';
import {
  getAllPrivacyPolicies,
  createPrivacyPolicies,
  updatePrivacyPolicies,
  deletePrivacyPolicies,
  searchPrivacyPolicies
} from '../../privacy-policies.service';

export default function PrivacyPoliciesMaster() {
  const [privacyPolicies, setPrivacyPolicies] = useState<PrivacyPolicies[]>([]);
  const [selectedPrivacyPolicies, setSelectedPrivacyPolicies] = useState<PrivacyPolicies | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<PrivacyPoliciesFilters>({
    search: '',
    language: '',
    language_code: '',
    dateRange: {
      startDate: '',
      endDate: ''
    }
  });

  // Load privacy policies
  const loadPrivacyPolicies = async () => {
    try {
      setLoading(true);
      const data = await getAllPrivacyPolicies();
      setPrivacyPolicies(data);
    } catch (error) {
      console.error('Error loading privacy policies:', error);
    } finally {
      setLoading(false);
    }
  };

  // Search privacy policies
  const handleSearch = async () => {
    try {
      setLoading(true);
      const data = await searchPrivacyPolicies(filters);
      setPrivacyPolicies(data);
    } catch (error) {
      console.error('Error searching privacy policies:', error);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadPrivacyPolicies();
  }, []);

  // Handle search when filters change
  useEffect(() => {
    if (filters.search || filters.language || filters.language_code || filters.dateRange.startDate || filters.dateRange.endDate) {
      handleSearch();
    } else {
      loadPrivacyPolicies();
    }
  }, [filters]);

  // Filter privacy policies locally
  const filteredPrivacyPolicies = privacyPolicies.filter(item => {
    const matchesSearch = !filters.search ||
      item.privacy_policy.toLowerCase().includes(filters.search.toLowerCase()) ||
      item.language.toLowerCase().includes(filters.search.toLowerCase()) ||
      item.language_code.toLowerCase().includes(filters.search.toLowerCase());

    const matchesLanguage = !filters.language || item.language === filters.language;
    const matchesLanguageCode = !filters.language_code || item.language_code === filters.language_code;

    return matchesSearch && matchesLanguage && matchesLanguageCode;
  });

  // Handle add new
  const handleAddNew = () => {
    setSelectedPrivacyPolicies(null);
    setFormMode('add');
    setIsFormOpen(true);
  };

  // Handle edit
  const handleEdit = (privacyPolicies: PrivacyPolicies) => {
    setSelectedPrivacyPolicies(privacyPolicies);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  // Handle view
  const handleView = (privacyPolicies: PrivacyPolicies) => {
    setSelectedPrivacyPolicies(privacyPolicies);
    setIsViewOpen(true);
  };

  // Handle delete
  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this privacy policy?')) {
      try {
        await deletePrivacyPolicies(id);
        await loadPrivacyPolicies();
      } catch (error) {
        console.error('Error deleting privacy policies:', error);
        alert('Failed to delete privacy policy');
      }
    }
  };

  // Handle form save
  const handleFormSave = async (formData: PrivacyPoliciesFormData) => {
    try {
      if (formMode === 'add') {
        await createPrivacyPolicies(formData);
      } else if (selectedPrivacyPolicies) {
        await updatePrivacyPolicies(selectedPrivacyPolicies.id, formData);
      }
      
      setIsFormOpen(false);
      setSelectedPrivacyPolicies(null);
      await loadPrivacyPolicies();
    } catch (error) {
      console.error('Error saving privacy policies:', error);
      throw error;
    }
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedPrivacyPolicies(null);
  };

  // Handle view close
  const handleViewClose = () => {
    setIsViewOpen(false);
    setSelectedPrivacyPolicies(null);
  };

  // Handle view edit
  const handleViewEdit = (privacyPolicies: PrivacyPolicies) => {
    setIsViewOpen(false);
    handleEdit(privacyPolicies);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="bg-gray-200 rounded-lg h-96"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Privacy Policies</h2>
          <p className="text-gray-600">Manage privacy policies for different languages</p>
        </div>
        <div className="flex items-center space-x-3">
          <button 
            onClick={loadPrivacyPolicies}
            className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors shadow-sm"
          >
            <i className="ri-refresh-line mr-2"></i>
            Refresh
          </button>
          <button 
            onClick={handleAddNew}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
          >
            <i className="ri-add-line mr-2"></i>
            Add Privacy Policy
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search policies, language..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select
              value={filters.language}
              onChange={(e) => setFilters(prev => ({ ...prev, language: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Languages</option>
              {LANGUAGE_OPTIONS.map(lang => (
                <option key={lang.code} value={lang.value}>{lang.label}</option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language Code</label>
            <select
              value={filters.language_code}
              onChange={(e) => setFilters(prev => ({ ...prev, language_code: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Codes</option>
              {LANGUAGE_OPTIONS.map(lang => (
                <option key={lang.code} value={lang.code}>{lang.code.toUpperCase()}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Privacy Policies List */}
      <PrivacyPoliciesList
        privacyPolicies={filteredPrivacyPolicies}
        onEdit={handleEdit}
        onView={handleView}
        onDelete={handleDelete}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isFormOpen}
        onClose={handleFormCancel}
        title={formMode === 'add' ? 'Add New Privacy Policy' : 'Edit Privacy Policy'}
        size="full"
        height="fixed"
      >
        <PrivacyPoliciesAddEdit
          privacyPolicies={selectedPrivacyPolicies}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
          mode={formMode}
        />
      </Modal>

      {/* View Modal */}
      <PrivacyPoliciesView
        privacyPolicies={selectedPrivacyPolicies}
        isOpen={isViewOpen}
        onClose={handleViewClose}
        onEdit={handleViewEdit}
      />
    </div>
  );
}
