// Currency Management Models and Types

export interface Currency {
  id: number;
  amount: number;
  created_at: string;
  currency_name: string;
  currency_symbol: string;
  currency_symbol_on_right: boolean;
  from_currency_code: string;
  is_disabled_conversion: boolean;
  is_disabled_currency: boolean;
  rate: number;
  timestamp: string;
  to_currency_code: string;
}

export interface CurrencyFormData {
  currency_name: string;
  currency_symbol: string;
  currency_symbol_on_right: boolean;
  is_disabled_conversion: boolean;
  is_disabled_currency: boolean;
  from_currency_code: string;
  to_currency_code: string;
}

export interface CurrencyFilters {
  search: string;
  is_disabled_currency?: boolean;
  is_disabled_conversion?: boolean;
  currency_code_filter: string;
}

export interface CurrencyStats {
  totalCurrencies: number;
  activeCurrencies: number;
  disabledCurrencies: number;
  lastUpdated: string;
}

export interface Pagination {
  current_page: number;
  page_size: number;
  total_items: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}

export interface CurrencyResponse {
  data: Currency[];
  pagination: Pagination;
  success: boolean;
  message?: string;
}

// Predefined currency data based on common international currencies
export const COMMON_CURRENCIES = [
  { code: 'USD', name: 'US Dollar', symbol: '$', decimalPlaces: 2 },
  { code: 'EUR', name: 'Euro', symbol: '€', decimalPlaces: 2 },
  { code: 'GBP', name: 'British Pound', symbol: '£', decimalPlaces: 2 },
  { code: 'JPY', name: 'Japanese Yen', symbol: '¥', decimalPlaces: 0 },
  { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', decimalPlaces: 2 },
  { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', decimalPlaces: 2 },
  { code: 'CHF', name: 'Swiss Franc', symbol: 'CHF', decimalPlaces: 2 },
  { code: 'CNY', name: 'Chinese Yuan', symbol: '¥', decimalPlaces: 2 },
  { code: 'SEK', name: 'Swedish Krona', symbol: 'kr', decimalPlaces: 2 },
  { code: 'NZD', name: 'New Zealand Dollar', symbol: 'NZ$', decimalPlaces: 2 },
  { code: 'MXN', name: 'Mexican Peso', symbol: '$', decimalPlaces: 2 },
  { code: 'SGD', name: 'Singapore Dollar', symbol: 'S$', decimalPlaces: 2 },
  { code: 'HKD', name: 'Hong Kong Dollar', symbol: 'HK$', decimalPlaces: 2 },
  { code: 'NOK', name: 'Norwegian Krone', symbol: 'kr', decimalPlaces: 2 },
  { code: 'KRW', name: 'South Korean Won', symbol: '₩', decimalPlaces: 0 },
  { code: 'TRY', name: 'Turkish Lira', symbol: '₺', decimalPlaces: 2 },
  { code: 'RUB', name: 'Russian Ruble', symbol: '₽', decimalPlaces: 2 },
  { code: 'INR', name: 'Indian Rupee', symbol: '₹', decimalPlaces: 2 },
  { code: 'BRL', name: 'Brazilian Real', symbol: 'R$', decimalPlaces: 2 },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', decimalPlaces: 2 },
  { code: 'PLN', name: 'Polish Zloty', symbol: 'zł', decimalPlaces: 2 },
  { code: 'DKK', name: 'Danish Krone', symbol: 'kr', decimalPlaces: 2 },
  { code: 'CZK', name: 'Czech Koruna', symbol: 'Kč', decimalPlaces: 2 },
  { code: 'HUF', name: 'Hungarian Forint', symbol: 'Ft', decimalPlaces: 0 },
  { code: 'ILS', name: 'Israeli Shekel', symbol: '₪', decimalPlaces: 2 },
  { code: 'CLP', name: 'Chilean Peso', symbol: '$', decimalPlaces: 0 },
  { code: 'PHP', name: 'Philippine Peso', symbol: '₱', decimalPlaces: 2 },
  { code: 'AED', name: 'UAE Dirham', symbol: 'د.إ', decimalPlaces: 2 },
  { code: 'COP', name: 'Colombian Peso', symbol: '$', decimalPlaces: 2 },
  { code: 'SAR', name: 'Saudi Riyal', symbol: '﷼', decimalPlaces: 2 },
  { code: 'MYR', name: 'Malaysian Ringgit', symbol: 'RM', decimalPlaces: 2 },
  { code: 'RON', name: 'Romanian Leu', symbol: 'lei', decimalPlaces: 2 },
  { code: 'HRK', name: 'Croatian Kuna', symbol: 'kn', decimalPlaces: 2 },
  { code: 'BGN', name: 'Bulgarian Lev', symbol: 'лв', decimalPlaces: 2 },
  { code: 'THB', name: 'Thai Baht', symbol: '฿', decimalPlaces: 2 },
  { code: 'IDR', name: 'Indonesian Rupiah', symbol: 'Rp', decimalPlaces: 0 },
  { code: 'EGP', name: 'Egyptian Pound', symbol: '£', decimalPlaces: 2 },
  { code: 'QAR', name: 'Qatari Riyal', symbol: '﷼', decimalPlaces: 2 },
  { code: 'KWD', name: 'Kuwaiti Dinar', symbol: 'د.ك', decimalPlaces: 3 },
  { code: 'BHD', name: 'Bahraini Dinar', symbol: '.د.ب', decimalPlaces: 3 }
];

// Currency utility functions
export const CurrencyUtils = {
  // Format currency amount with proper symbol and decimal places
  formatAmount: (amount: number, currency: Currency): string => {
    const formattedAmount = amount.toFixed(2);
    if (currency.currency_symbol_on_right) {
      return `${formattedAmount} ${currency.currency_symbol}`;
    }
    return `${currency.currency_symbol}${formattedAmount}`;
  },

  // Format amount with custom symbol
  formatAmountWithSymbol: (amount: number, symbol: string, symbolOnRight: boolean = false): string => {
    const formattedAmount = amount.toFixed(2);
    if (symbolOnRight) {
      return `${formattedAmount} ${symbol}`;
    }
    return `${symbol}${formattedAmount}`;
  },

  // Get currency by code
  getCurrencyByCode: (currencies: Currency[], code: string): Currency | undefined => {
    return currencies.find(currency => currency.to_currency_code === code);
  },

  // Validate currency code format (ISO 4217)
  isValidCurrencyCode: (code: string): boolean => {
    return /^[A-Z]{3}$/.test(code);
  },

  // Sort currencies by code
  sortByCode: (currencies: Currency[]): Currency[] => {
    return [...currencies].sort((a, b) => a.to_currency_code.localeCompare(b.to_currency_code));
  },

  // Sort currencies by name
  sortByName: (currencies: Currency[]): Currency[] => {
    return [...currencies].sort((a, b) => a.currency_name.localeCompare(b.currency_name));
  },

  // Filter active currencies
  getActiveCurrencies: (currencies: Currency[]): Currency[] => {
    return currencies.filter(currency => !currency.is_disabled_currency);
  },

  // Convert amount using rate
  convertAmount: (amount: number, rate: number): number => {
    return amount * rate;
  },

  // Format rate display
  formatRate: (rate: number): string => {
    return rate.toFixed(6);
  }
};

export default Currency;
