'use client';

import React, { useState, useEffect } from 'react';
import { 
  PrivacyPolicies, 
  PrivacyPoliciesFormData, 
  PrivacyPoliciesFormErrors,
  getDefaultPrivacyPoliciesFormData,
  validatePrivacyPoliciesForm,
  LANGUAGE_OPTIONS
} from '../../privacy-policies.model';

interface PrivacyPoliciesAddEditProps {
  privacyPolicies: PrivacyPolicies | null;
  onSave: (formData: PrivacyPoliciesFormData) => Promise<void>;
  onCancel: () => void;
  mode: 'add' | 'edit';
}

export default function PrivacyPoliciesAddEdit({ 
  privacyPolicies, 
  onSave, 
  onCancel, 
  mode 
}: PrivacyPoliciesAddEditProps) {
  const [formData, setFormData] = useState<PrivacyPoliciesFormData>(getDefaultPrivacyPoliciesFormData());
  const [errors, setErrors] = useState<PrivacyPoliciesFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data
  useEffect(() => {
    if (mode === 'edit' && privacyPolicies) {
      setFormData({
        privacy_policy: privacyPolicies.privacy_policy || '',
        language: privacyPolicies.language || '',
        language_code: privacyPolicies.language_code || '',
      });
    } else {
      setFormData(getDefaultPrivacyPoliciesFormData());
    }
    setErrors({});
  }, [privacyPolicies, mode]);

  // Handle input changes
  const handleInputChange = (field: keyof PrivacyPoliciesFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Handle language selection
  const handleLanguageChange = (languageName: string) => {
    const selectedLanguage = LANGUAGE_OPTIONS.find(lang => lang.value === languageName);
    if (selectedLanguage) {
      setFormData(prev => ({
        ...prev,
        language: selectedLanguage.value,
        language_code: selectedLanguage.code
      }));
      
      // Clear errors
      setErrors(prev => ({ 
        ...prev, 
        language: undefined, 
        language_code: undefined 
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validationErrors = validatePrivacyPoliciesForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
    } catch (error) {
      console.error('Error saving privacy policies:', error);
      alert('Failed to save privacy policy. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex-shrink-0 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">
              {mode === 'add' ? 'Add New Privacy Policy' : 'Edit Privacy Policy'}
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              {mode === 'add' 
                ? 'Create privacy policy for a specific language' 
                : 'Update the privacy policy content'
              }
            </p>
          </div>
        </div>
      </div>

      {/* Form */}
      <div className="flex-1 overflow-y-auto">
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Language Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Language <span className="text-red-500">*</span>
              </label>
              <select
                value={formData.language}
                onChange={(e) => handleLanguageChange(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.language ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSubmitting}
              >
                <option value="">Select Language</option>
                {LANGUAGE_OPTIONS.map(lang => (
                  <option key={lang.code} value={lang.value}>{lang.label}</option>
                ))}
              </select>
              {errors.language && (
                <p className="mt-1 text-sm text-red-600">{errors.language}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Language Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.language_code}
                onChange={(e) => handleInputChange('language_code', e.target.value)}
                placeholder="e.g., en, es, fr"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.language_code ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSubmitting}
                maxLength={3}
              />
              {errors.language_code && (
                <p className="mt-1 text-sm text-red-600">{errors.language_code}</p>
              )}
            </div>
          </div>

          {/* Privacy Policy Content */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Privacy Policy Content <span className="text-red-500">*</span>
            </label>
            <textarea
              value={formData.privacy_policy}
              onChange={(e) => handleInputChange('privacy_policy', e.target.value)}
              placeholder="Enter the complete privacy policy content..."
              rows={15}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
                errors.privacy_policy ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isSubmitting}
            />
            {errors.privacy_policy && (
              <p className="mt-1 text-sm text-red-600">{errors.privacy_policy}</p>
            )}
            <p className="mt-1 text-sm text-gray-500">
              Character count: {formData.privacy_policy.length}
            </p>
          </div>

          {/* Preview */}
          {formData.privacy_policy && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preview
              </label>
              <div className="border border-gray-300 rounded-lg p-4 bg-gray-50 max-h-40 overflow-y-auto">
                <div className="text-sm text-gray-700 whitespace-pre-wrap">
                  {formData.privacy_policy.substring(0, 500)}
                  {formData.privacy_policy.length > 500 && '...'}
                </div>
              </div>
            </div>
          )}
        </form>
      </div>

      {/* Footer */}
      <div className="flex-shrink-0 px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg font-medium hover:bg-gray-50 transition-colors"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                {mode === 'add' ? 'Creating...' : 'Updating...'}
              </>
            ) : (
              <>
                <i className={`${mode === 'add' ? 'ri-add-line' : 'ri-save-line'} mr-2`}></i>
                {mode === 'add' ? 'Create Privacy Policy' : 'Update Privacy Policy'}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
