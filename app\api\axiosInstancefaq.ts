import axios, {
  AxiosResponse,
  InternalAxiosRequestConfig,
} from 'axios';

// Create Axios instance
const axiosInstancefaq = axios.create({
  // baseURL: process.env.NEXT_PUBLIC_API_BASE_URL ?? 'http://*************:8080/api/v1/',
    baseURL: 'http://**************:30004',

});


// Request interceptor  
axiosInstancefaq.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Guard against SSR       
    if (typeof window === 'undefined') return config;

    const token = localStorage.getItem('token');

    // Skip modification for static JSON files
    if (config.url?.includes('/json/')) {
      return config;
    }

    // Handle FormData upload requests
    if (config.url?.includes('formData/')) {
      config.url = config.url.replace('formData/', '');
      if (token) config.headers.set('Authorization', `Bearer ${token}`);
      return config;
    }

    // Default headers for JSON requests
    if (token) config.headers.set('Authorization', `Bearer ${token}`);
    if (!config.headers.has('Content-Type') && config.data) {
      config.headers.set('Content-Type', 'application/json');
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
axiosInstancefaq.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    const status = error.response?.status;

    if (status === 401 || status === 403) {
      if (typeof window !== 'undefined') {
        // window.location.href = '/';
      }
    }

    return Promise.reject(error);
  }
);

export default axiosInstancefaq;
