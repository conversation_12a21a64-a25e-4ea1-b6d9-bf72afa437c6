@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;

  /* Header Variables */
  --color-header-bg: #ffffff;
  --color-header-border: #e2e8f0;

  /* Sidebar Variables */
  --color-sidebar-bg: #1e293b;
  --color-sidebar-text: #cbd5e1;
  --color-sidebar-text-active: #ffffff;
  --color-sidebar-border: #334155;

  /* Primary Colors */
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;

  /* Text Colors */
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-text-muted: #94a3b8;

  /* Surface Colors */
  --color-surface-alt: #f8fafc;
  --color-border: #e2e8f0;

  /* Status Colors */
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;

    /* Header Variables - Dark */
    --color-header-bg: #1e293b;
    --color-header-border: #334155;

    /* Sidebar Variables - Dark */
    --color-sidebar-bg: #0f172a;
    --color-sidebar-text: #94a3b8;
    --color-sidebar-text-active: #ffffff;
    --color-sidebar-border: #1e293b;

    /* Text Colors - Dark */
    --color-text-primary: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-tertiary: #94a3b8;
    --color-text-muted: #64748b;

    /* Surface Colors - Dark */
    --color-surface-alt: #1e293b;
    --color-border: #334155;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Responsive Typography */
.text-responsive-sm {
  font-size: 0.875rem;
}

.text-responsive-lg {
  font-size: 1.125rem;
}

@media (max-width: 768px) {
  .text-responsive-sm {
    font-size: 0.8rem;
  }

  .text-responsive-lg {
    font-size: 1rem;
  }
}

/* Responsive Padding */
.px-responsive {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 1024px) {
  .px-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Touch Targets for Mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Input Styles */
.input {
  border-radius: 0.5rem;
  transition: all 0.2s ease-in-out;
}

/* Custom Scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input:focus {
  outline: none;
  ring: 2px;
  ring-color: var(--color-primary-600);
  border-color: var(--color-primary-600);
}

/* Font Families */
.font-pacifico {
  font-family: 'Pacifico', cursive;
}

/* Layout Classes */
.sidebar-expanded {
  margin-left: 16rem; /* 64 * 0.25rem = 16rem for w-64 */
}

.sidebar-collapsed {
  margin-left: 4rem; /* 16 * 0.25rem = 4rem for w-16 */
}

@media (max-width: 768px) {
  .sidebar-expanded,
  .sidebar-collapsed {
    margin-left: 0;
  }
}

/* Fixed Layout Styles */
.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 4rem;
  z-index: 50;
}

.fixed-sidebar {
  position: fixed;
  left: 0;
  top: 4rem;
  bottom: 0;
  z-index: 40;
}

.main-content {
  padding-top: 4rem;
  transition: margin-left 0.3s ease-in-out;
}

/* Smooth transitions for sidebar */
.sidebar-transition {
  transition: all 0.3s ease-in-out;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Sidebar specific scrollbar styling */
.sidebar-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(203, 213, 225, 0.2) transparent;
  transition: scrollbar-color 0.3s ease;
}

.sidebar-scrollbar:hover {
  scrollbar-color: rgba(203, 213, 225, 0.4) transparent;
}

.sidebar-scrollbar::-webkit-scrollbar {
  width: 6px;
  transition: width 0.2s ease;
}

.sidebar-scrollbar:hover::-webkit-scrollbar {
  width: 8px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 6px;
  margin: 4px 0;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.2);
  border-radius: 6px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.3s ease;
  min-height: 20px;
}

.sidebar-scrollbar:hover::-webkit-scrollbar-thumb {
  background: rgba(203, 213, 225, 0.4);
  border: 1px solid transparent;
  background-clip: content-box;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(203, 213, 225, 0.6);
  background-clip: content-box;
  transform: scaleX(1.1);
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:active {
  background: rgba(203, 213, 225, 0.8);
  background-clip: content-box;
  transform: scaleX(1.2);
}

/* Dark mode sidebar scrollbar */
@media (prefers-color-scheme: dark) {
  .sidebar-scrollbar {
    scrollbar-color: rgba(148, 163, 184, 0.2) transparent;
  }

  .sidebar-scrollbar:hover {
    scrollbar-color: rgba(148, 163, 184, 0.4) transparent;
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.2);
  }

  .sidebar-scrollbar:hover::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.4);
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(148, 163, 184, 0.6);
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb:active {
    background: rgba(148, 163, 184, 0.8);
  }
}

/* Sidebar scroll fade effects */
.sidebar-scroll-container {
  position: relative;
}

.sidebar-scroll-container::before,
.sidebar-scroll-container::after {
  content: '';
  position: absolute;
  left: 0;
  right: 8px; /* Account for scrollbar width */
  height: 12px;
  pointer-events: none;
  z-index: 10;
  transition: opacity 0.3s ease;
}

.sidebar-scroll-container::before {
  top: 0;
  background: linear-gradient(to bottom, var(--color-sidebar-bg) 0%, transparent 100%);
}

.sidebar-scroll-container::after {
  bottom: 0;
  background: linear-gradient(to top, var(--color-sidebar-bg) 0%, transparent 100%);
}

/* Enhanced scrollbar corner styling */
.sidebar-scrollbar::-webkit-scrollbar-corner {
  background: transparent;
}

/* Scrollbar button styling (for browsers that show them) */
.sidebar-scrollbar::-webkit-scrollbar-button {
  display: none;
}

/* Auto-hide scrollbar when not needed */
.sidebar-scrollbar {
  overflow-y: auto;
  overflow-x: hidden;
}

/* Smooth scrolling behavior */
.sidebar-scrollbar {
  scroll-behavior: smooth;
}

/* Additional polish for sidebar scrollbar */
.sidebar-scrollbar::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
  box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.2);
}

/* Focus styles for accessibility */
.sidebar-scrollbar:focus-visible {
  outline: 2px solid var(--color-primary-600);
  outline-offset: -2px;
}

/* Responsive scrollbar for mobile */
@media (max-width: 768px) {
  .sidebar-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .sidebar-scrollbar:hover::-webkit-scrollbar {
    width: 6px;
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb {
    border: 1px solid transparent;
  }
}

/* Custom scrollbar styles for home page */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgb(203 213 225) transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgb(203 213 225);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgb(148 163 184);
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
