'use client';

import { useState } from 'react';
import CurrencyMaster from './components/CurrencyMaster';

export default function CurrencyPage() {
  const [lastSyncTime] = useState(new Date().toLocaleTimeString());

  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Professional Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                  <i className="ri-money-dollar-circle-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Currency Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Manage exchange rates, currency settings, and financial configurations
                  </p>
                </div>
              </div>
            </div>

            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
                <i className="ri-time-line"></i>
                <span>Last sync: {lastSyncTime}</span>
              </div>
              <button className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors shadow-sm">
                <i className="ri-download-line mr-2"></i>
                Export
              </button>
              <button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors shadow-sm">
                <i className="ri-refresh-line mr-2"></i>
                Sync Rates
              </button>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-coins-line text-blue-600 text-sm"></i>
                </div>
                <div>
                  <p className="text-xs text-gray-600">Total Currencies</p>
                  <p className="text-lg font-semibold text-gray-900">--</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-check-line text-green-600 text-sm"></i>
                </div>
                <div>
                  <p className="text-xs text-gray-600">Active</p>
                  <p className="text-lg font-semibold text-gray-900">--</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-star-line text-yellow-600 text-sm"></i>
                </div>
                <div>
                  <p className="text-xs text-gray-600">Base Currency</p>
                  <p className="text-lg font-semibold text-gray-900">--</p>
                </div>
              </div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-time-line text-purple-600 text-sm"></i>
                </div>
                <div>
                  <p className="text-xs text-gray-600">Last Updated</p>
                  <p className="text-lg font-semibold text-gray-900">--</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area with Internal Scrolling */}
      <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
        <div className="p-6">
          <div className="max-w-full">
            <div className="animate-fade-in">
              <CurrencyMaster />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
