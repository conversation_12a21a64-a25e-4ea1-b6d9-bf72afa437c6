import apiService from "../api/api-service"
import { hotelDetail, HotelListApiResponse } from "./hotel-master.model"

export const getHotelListApi = async (skip: number, limit: number, filters: any) : Promise<HotelListApiResponse> => {
    // Build query parameters
    const params = new URLSearchParams({
        skip: skip.toString(),
        limit: limit.toString()
    });

    // Add filters to query parameters if they exist
    if (filters) {
        Object.keys(filters).forEach(key => {
            const value = filters[key];
            if (value !== null && value !== undefined && value !== '' && value !== false) {
                params.append(key, value.toString());
            }
        });
    }

    // return apiService.get<HotelListApiResponse>(`/hotels?${params.toString()}/`)
    return apiService.get<HotelListApiResponse>(`/hotels/`)

}

export const getHotelDetailsApi = async (id: string) : Promise<hotelDetail> => {
    return apiService.get<hotelDetail>(`/hotels/${id}/details`)
}

export const editHotelApi = async (id: string, hotel: hotelDetail) : Promise<hotelDetail> => {
    return apiService.put<hotelDetail>(`/hotels/${id}`, hotel)
}