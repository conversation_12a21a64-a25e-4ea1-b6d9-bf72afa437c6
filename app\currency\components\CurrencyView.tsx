'use client';

import { Currency, CurrencyUtils } from '../currency.model';

interface CurrencyViewProps {
  currency: Currency | null;
  onEdit: () => void;
  onClose: () => void;
}

export default function CurrencyView({ currency, onEdit, onClose }: CurrencyViewProps) {
  if (!currency) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No currency selected</p>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount: number) => {
    if (currency.currency_symbol_on_right) {
      return `${amount.toFixed(2)} ${currency.currency_symbol}`;
    }
    return `${currency.currency_symbol}${amount.toFixed(2)}`;
  };

  return (
    <div className="space-y-6">
      {/* <PERSON><PERSON><PERSON>cy Header */}
      <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
        <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-xl flex items-center justify-center">
          <span className="text-green-700 font-bold text-2xl">{currency.currency_symbol}</span>
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-gray-900">{currency.currency_name}</h2>
          <p className="text-lg text-gray-600">{currency.to_currency_code}</p>
          <div className="flex items-center space-x-4 mt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              !currency.is_disabled_currency
                ? 'bg-green-100 text-green-800'
                : 'bg-red-100 text-red-800'
            }`}>
              <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
                !currency.is_disabled_currency ? 'bg-green-400' : 'bg-red-400'
              }`}></span>
              {!currency.is_disabled_currency ? 'Active' : 'Disabled'}
            </span>
            {currency.is_disabled_conversion && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                <i className="ri-close-circle-line mr-1"></i>
                Conversion Disabled
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Currency Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Currency Code</label>
              <p className="text-lg font-semibold text-gray-900">{currency.to_currency_code}</p>
              <p className="text-xs text-gray-500">ISO 4217 Standard</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Currency Name</label>
              <p className="text-lg font-semibold text-gray-900">{currency.currency_name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Symbol</label>
              <p className="text-lg font-semibold text-gray-900">{currency.currency_symbol}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Symbol Position</label>
              <p className="text-lg font-semibold text-gray-900">
                {currency.currency_symbol_on_right ? 'Right (100€)' : 'Left ($100)'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">From Currency</label>
              <p className="text-lg font-semibold text-gray-900">{currency.from_currency_code}</p>
            </div>
          </div>
        </div>

        {/* Exchange Rate Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Exchange Rate</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Base Amount</label>
              <p className="text-2xl font-bold text-blue-600">{formatAmount(currency.amount)}</p>
              <p className="text-xs text-gray-500">Base amount for conversion</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Exchange Rate</label>
              <p className="text-2xl font-bold text-green-600">{currency.rate.toFixed(6)}</p>
              <p className="text-xs text-gray-500">
                1 {currency.from_currency_code} = {currency.rate.toFixed(6)} {currency.to_currency_code}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-lg font-semibold text-gray-900">{formatDate(currency.timestamp)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Status</label>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  !currency.is_disabled_currency
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {!currency.is_disabled_currency ? 'Active' : 'Disabled'}
                </span>
                {currency.is_disabled_conversion && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    <i className="ri-close-circle-line mr-1"></i>
                    Conversion Disabled
                  </span>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* System Information */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-500">Currency ID</label>
            <p className="text-sm font-mono text-gray-900 bg-gray-100 px-2 py-1 rounded">
              {currency.id}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Created Date</label>
            <p className="text-sm text-gray-900">{formatDate(currency.created_at)}</p>
            {currency.created_by && (
              <p className="text-xs text-gray-500">by {currency.created_by}</p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-500">Last Updated</label>
            <p className="text-sm text-gray-900">{formatDate(currency.updated_at)}</p>
            {currency.updated_by && (
              <p className="text-xs text-gray-500">by {currency.updated_by}</p>
            )}
          </div>
        </div>
      </div>

      {/* Example Conversions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Example Amounts</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[1, 10, 100, 1000].map((amount) => (
            <div key={amount} className="text-center p-3 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-500">Amount</p>
              <p className="text-lg font-semibold text-gray-900">
                {formatAmount(amount)}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          Close
        </button>
        <button
          onClick={onEdit}
          className="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-lg hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        >
          <i className="ri-edit-line mr-2"></i>
          Edit Currency
        </button>
      </div>
    </div>
  );
}
