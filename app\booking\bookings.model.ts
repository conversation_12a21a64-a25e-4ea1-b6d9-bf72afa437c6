export interface BookingApiResponse {
  id: number;
  booking_reference: string;
  service_type: 'HOTEL';
  status: 'FAILED' | 'PENDING' | 'SUCCESS';
  payment_status: 'UNPAID | PAID';
  user_id: number;
  created_at: string;
  updated_at: string;
  hotel_booking: HotelBooking;
}

export interface HotelBooking {
  id: number;
  master_booking_reference: string;
  provider_booking_id: string | null;
  booking_ref_id: string;
  hotel_id: string;
  search_key: string;
  rate_ids: string[];
  rooms_allocations: RoomAllocation[];
  billing_contact: BillingContact;
  credit_card_info: null;
  special_requests: null;
  provider_response: ProviderResponse | null;
  booking_snapshot?: BookingSnapshot;
}

export interface BookingSnapshot {
  id: string;
  hotelName: string | null;
  rates: BookingSnapshotRate[];
  rooms: BookingSnapshotRoom[];
}

export interface BookingSnapshotRate {
  id: string;
  baseRate: number;
  totalRate: number;
  currency: string;
  refundability: string;
  payAtHotel: boolean;
  taxes: BookingSnapshotTax[];
  cancellationPolicies: BookingSnapshotCancellationPolicy[];
}

export interface BookingSnapshotTax {
  amount: number;
  description: string;
}

export interface BookingSnapshotCancellationPolicy {
  rules: BookingSnapshotRule[];
}

export interface BookingSnapshotRule {
  start: string;
  end: string;
  value: number;
  valueType: string;
}

export interface BookingSnapshotRoom {
  id: string;
  name: string;
  description: string;
  facilities: BookingSnapshotFacility[];
}

export interface BookingSnapshotFacility {
  name: string;
}

export interface RoomAllocation {
  roomid: string;
  rateid: string;
  guests: Guest[];
}

export interface Guest {
  type: 'Adult';
  title: 'Mr' | 'Miss';
  firstname: string;
  lastname: string;
  age: number;
  email: string;
}

export interface BillingContact {
  title: 'Mr' | 'Miss';
  firstName: string;
  lastName: string;
  age: number;
  contact: Contact;
}

export interface Contact {
  phone: string;
  address: Address;
  email: string;
}

export interface Address {
  line1: string;
  line2: string;
  city: City;
  state: State;
  country: Country;
  postalCode: string;
}

export interface City {
  name: string;
  code: string;
}

export interface State {
  name: string;
  code: string;
}

export interface Country {
  name: string;
  code: string;
}

export interface ProviderResponse {
  hotelBooking: ProviderHotelBooking;
}

export interface ProviderHotelBooking {
  billingContact: ProviderBillingContact;
  bookingId: string;
  bookingStatus: 'Initiated | Cancelled';
  bookingType: 'Online';
  cancellationDate: string;
  channelId: string;
  correlationId: string;
  countryOfResidence: string;
  creationDate: string;
  culture: string;
  email: string;
  guestNames: string;
  guests: string[];
  hotel: HotelDetails;
  hotel_id: string;
  isModified: boolean;
  nationality: string;
  providerId: string;
  providerName: string;
  rates: Rate[];
  rooms: Room[];
  roomsAllocations: ProviderRoomAllocation[];
  search_key: string;
  supplierRates: SupplierRate[];
  totalRate: number;
  tripEndDate: string;
  tripStartDate: string;
}

export interface ProviderBillingContact {
  age: string;
  contact: ProviderContact;
  firstName: string;
  lastName: string;
  title: 'Mr' | 'Miss';
}

export interface ProviderContact {
  address: ProviderAddress;
  email: string;
  phone: string;
}

export interface ProviderAddress {
  city: City;
  country: Country;
  line1: string;
  line2: string;
  postalCode: string;
  state: State;
}

export interface HotelDetails {
  category: string;
  chainName: string;
  checkinInfo: CheckinInfo;
  checkoutInfo: CheckoutInfo;
  contact: HotelContact;
  descriptions: Description[];
  distance: string;
  facilities: Facility[];
  geoCode: GeoCode;
  heroImage: string;
  id: string;
  name: string;
  policies: Policy[];
  providerHotelId: string;
  providerId: string;
  relevanceScore: string;
  starRating: string;
  type?: string;
}

export interface CheckinInfo {
  beginTime: string;
  minAge: string;
}

export interface CheckoutInfo {
  time: string;
}

export interface HotelContact {
  address: HotelAddress;
  email?: string;
  phone: string;
}

export interface HotelAddress {
  city: City;
  country: Country;
  line1: string;
  state: State;
  line2?: string;
  postalCode?: string;
}

export interface Description {
  text: string;
  type: string;
}

export interface Facility {
  name: string;
}

export interface GeoCode {
  lat: string;
  long: string;
}

export interface Policy {
  text: string;
  type: string;
}

export interface Rate {
  IsPANMandatory: boolean;
  IsPassportMandatory: boolean;
  additionalCharges: AdditionalCharge[];
  allGuestsInfoRequired: boolean;
  availability: string;
  baseRate: number;
  boardBasis: BoardBasis;
  cancellationPolicies: CancellationPolicy[];
  cardRequired: boolean;
  conversionRate: ConversionRate;
  currency: string;
  dailyRates: DailyRate[];
  depositRequired: boolean;
  deposits: any[];
  distributionChannel: string;
  distributionType: string;
  guaranteeRequired: boolean;
  id: string;
  isChildConvertedToAdult: boolean;
  isContractedRate: boolean;
  minSellingRate: number;
  needsPriceCheck: boolean;
  occupancies: Occupancy[];
  offers: any[];
  onlineCancellable: boolean;
  payAtHotel: boolean;
  policies: RatePolicy[];
  providerHotelId: string;
  providerId: string;
  providerName: string;
  publishedBaseRate: number;
  publishedRate: number;
  refundability: 'NonRefundable';
  refundable: boolean;
  specialRequestSupported: boolean;
  taxes: Tax[];
  totalRate: number;
  traderInformation: TraderInformation;
  type: 'Negotiated';
  usdconversionRate: UsdConversionRate;
}

export interface AdditionalCharge {
  charge: Charge;
  text: string;
}

export interface Charge {
  amount: number;
  currency: 'AED';
  description: string;
  frequency: 'Unknown';
  type: 'Fee';
  unit: 'Unknown';
}

export interface BoardBasis {
  description: string;
  type: 'RoomOnly';
}

export interface CancellationPolicy {
  rules: Rule[];
}

export interface Rule {
  end: string;
  estimatedValue: number;
  start: string;
  value: number;
  valueType: 'Amount';
}

export interface ConversionRate {
  conversionFactor: number;
  fromCurrency: 'USD';
  toCurrency: 'INR';
}

export interface DailyRate {
  amount: number;
  date: string;
  discount: number;
  taxIncluded: boolean;
}

export interface Occupancy {
  childAges: any[];
  numOfAdults: string;
  numOfChildren: string;
  roomId: string;
}

export interface RatePolicy {
  text: string;
  type: string;
}

export interface Tax {
  amount: number;
  description: string;
  isIncludedInBaseRate: boolean;
}

export interface TraderInformation {
  trader: any[];
}

export interface UsdConversionRate {
  conversionFactor: number;
  fromCurrency: 'INR' | 'USD';
  toCurrency: 'USD';
}

export interface Room {
  beds: Bed[];
  description: string;
  facilities: Facility[];
  id: string;
  maxGuestAllowed: string;
  name: string;
  smokingAllowed: boolean;
  views: any[];
}

export interface Bed {
  count: string;
  type?: string;
}

export interface ProviderRoomAllocation {
  guests: ProviderGuest[];
  rateId: string;
  roomId: string;
}

export interface ProviderGuest {
  age: string;
  email: string;
  firstName: string;
  lastName: string;
  title: 'Mr' | 'Miss';
  type: 'Adult';
}

export interface SupplierRate {
  IsPANMandatory: boolean;
  IsPassportMandatory: boolean;
  additionalCharges: AdditionalCharge[];
  allGuestsInfoRequired: boolean;
  availability: string;
  baseRate: number;
  boardBasis: BoardBasis;
  cancellationPolicies: CancellationPolicy[];
  cardRequired: boolean;
  currency: 'USD' | 'INR';
  dailyRates: DailyRate[];
  depositRequired: boolean;
  deposits: any[];
  distributionChannel: 'Any';
  distributionType: 'Unknown';
  guaranteeRequired: boolean;
  id: string;
  isChildConvertedToAdult: boolean;
  isContractedRate: boolean;
  minSellingRate: number;
  needsPriceCheck: boolean;
  occupancies: Occupancy[];
  offers: any[];
  onlineCancellable: boolean;
  payAtHotel: boolean;
  policies: RatePolicy[];
  providerHotelId: string;
  providerId: string;
  providerName: string;
  publishedBaseRate: number;
  publishedRate: number;
  refundability: 'NonRefundable';
  refundable: boolean;
  specialRequestSupported: boolean;
  taxes: Tax[];
  totalRate: number;
  traderInformation: TraderInformation;
  type: 'Negotiated';
  usdconversionRate: UsdConversionRate;
}

export interface booking{
    bookingId:string;
    serviceType:string;
    status:string;
    paymentStatus:string;
    bookingStatus:string;
    userId:string;
    createdAt:string;
    updatedAt:string;
    bookingReference:string;
    guestName: bookingListGuest;

}

export interface bookingListGuest{
    title:string;
    firstName:string;
    lastName:string;
    age:string;
    phone:string;
    email:string
}