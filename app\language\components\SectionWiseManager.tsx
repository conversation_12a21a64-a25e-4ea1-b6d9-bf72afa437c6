'use client';

import { useState, useRef } from 'react';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import { updateLanguageTranslations } from '../language.service';

interface SectionWiseManagerProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  readOnly?: boolean;
  languageName?: string;
  languageId?: number;
  onTranslationsSaved?: (success: boolean, message: string) => void;
}

interface TranslationRow {
  section: string;
  key: string;
  fullPath: string;
  value: string;
}

export default function SectionWiseManager({ 
  value, 
  onChange, 
  readOnly = false, 
  languageName = 'Language',
  languageId,
  onTranslationsSaved
}: SectionWiseManagerProps) {
  const [processingSection, setProcessingSection] = useState<string | null>(null);
  const [sectionStatus, setSectionStatus] = useState<Record<string, {
    type: 'success' | 'error' | 'info' | null;
    message: string;
  }>>({});
  const [autoSave, setAutoSave] = useState(true);
  
  // Create refs for each section's file input
  const fileInputRefs = useRef<Record<string, HTMLInputElement | null>>({});

  // Get all sections from the translation data
  const sections = Object.keys(value).filter(key => 
    typeof value[key] === 'object' && value[key] !== null
  );

  // Flatten translations for a specific section
  const flattenSection = (sectionData: Record<string, any>, sectionName: string): TranslationRow[] => {
    const rows: TranslationRow[] = [];
    
    Object.entries(sectionData).forEach(([key, val]) => {
      rows.push({
        section: sectionName,
        key: key,
        fullPath: `${sectionName}.${key}`,
        value: String(val || '')
      });
    });
    
    return rows;
  };

  // Build section data from Excel rows
  const buildSectionFromExcel = (rows: TranslationRow[]): Record<string, any> => {
    const result: Record<string, any> = {};
    
    rows.forEach(row => {
      const pathParts = row.fullPath.split('.');
      if (pathParts.length >= 2) {
        const sectionName = pathParts[0];
        const keyName = pathParts[1];
        
        if (!result[sectionName]) {
          result[sectionName] = {};
        }
        result[sectionName][keyName] = row.value || '';
      }
    });
    
    return result;
  };

  // Export specific section to Excel
  const exportSection = (sectionName: string) => {
    try {
      setProcessingSection(sectionName);
      
      const sectionData = value[sectionName];
      if (!sectionData) {
        setSectionStatus(prev => ({
          ...prev,
          [sectionName]: { type: 'error', message: 'Section data not found' }
        }));
        setProcessingSection(null);
        return;
      }

      const rows = flattenSection(sectionData, sectionName);
      
      // Prepare data for Excel
      const excelData = rows.map(row => ({
        'Section': row.section,
        'Key': row.key,
        'Full Path': row.fullPath,
        'Translation Value': row.value,
        'Notes': '' // Empty column for user notes
      }));

      // Create workbook and worksheet
      const wb = XLSX.utils.book_new();
      const ws = XLSX.utils.json_to_sheet(excelData);

      // Set column widths
      const colWidths = [
        { wch: 15 }, // Section
        { wch: 20 }, // Key
        { wch: 30 }, // Full Path
        { wch: 40 }, // Translation Value
        { wch: 20 }  // Notes
      ];
      ws['!cols'] = colWidths;

      // Add section info at the top
      XLSX.utils.sheet_add_aoa(ws, [
        [`Section: ${sectionName.toUpperCase()}`],
        [`Total Items: ${rows.length}`],
        [`Generated: ${new Date().toLocaleString()}`],
        [] // Empty row before data
      ], { origin: 'A1' });

      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(wb, ws, sectionName);

      // Generate Excel file and download
      const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
      const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      
      const fileName = `${languageName.replace(/[^a-zA-Z0-9]/g, '_')}_${sectionName.toUpperCase()}_${new Date().toISOString().split('T')[0]}.xlsx`;
      saveAs(data, fileName);

      setSectionStatus(prev => ({
        ...prev,
        [sectionName]: { 
          type: 'success', 
          message: `${sectionName} section exported successfully! ${rows.length} items.` 
        }
      }));

    } catch (error) {
      console.error(`Export error for ${sectionName}:`, error);
      setSectionStatus(prev => ({
        ...prev,
        [sectionName]: { type: 'error', message: 'Failed to export section' }
      }));
    } finally {
      setProcessingSection(null);
    }
  };

  // Handle file upload for specific section
  const handleSectionUpload = (sectionName: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setProcessingSection(sectionName);
    setSectionStatus(prev => ({
      ...prev,
      [sectionName]: { type: 'info', message: 'Processing Excel file...' }
    }));

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        
        // Get first worksheet
        const worksheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[worksheetName];
        
        // Convert to JSON (skip header rows)
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { range: 4 }) as any[];
        
        // Validate and transform data
        const translationRows: TranslationRow[] = [];
        const errors: string[] = [];
        
        jsonData.forEach((row, index) => {
          const rowNum = index + 5; // Account for header rows
          
          if (!row['Full Path'] || row['Full Path'].toString().trim() === '') {
            errors.push(`Row ${rowNum}: Missing 'Full Path'`);
            return;
          }
          
          if (row['Translation Value'] === undefined || row['Translation Value'] === null) {
            row['Translation Value'] = '';
          }
          
          translationRows.push({
            section: sectionName,
            key: row['Key'] || '',
            fullPath: row['Full Path'].toString().trim(),
            value: row['Translation Value'].toString()
          });
        });

        if (errors.length > 0) {
          setSectionStatus(prev => ({
            ...prev,
            [sectionName]: {
              type: 'error',
              message: `Validation errors: ${errors.slice(0, 3).join(', ')}${errors.length > 3 ? '...' : ''}`
            }
          }));
          setProcessingSection(null);
          return;
        }

        // Build section structure and update
        const sectionStructure = buildSectionFromExcel(translationRows);
        const updatedTranslations = {
          ...value,
          ...sectionStructure
        };
        
        onChange(updatedTranslations);

        // Auto-save if enabled
        if (autoSave && languageId && !readOnly) {
          try {
            setSectionStatus(prev => ({
              ...prev,
              [sectionName]: { type: 'info', message: 'Saving to server...' }
            }));

            await updateLanguageTranslations(languageId, updatedTranslations);
            
            setSectionStatus(prev => ({
              ...prev,
              [sectionName]: { 
                type: 'success', 
                message: `${sectionName} section imported and saved successfully! ${translationRows.length} items.` 
              }
            }));

            onTranslationsSaved?.(true, `${sectionName} section saved successfully`);

          } catch (saveError) {
            console.error('Auto-save error:', saveError);
            setSectionStatus(prev => ({
              ...prev,
              [sectionName]: { 
                type: 'error', 
                message: `${sectionName} section imported but failed to save to server.` 
              }
            }));

            onTranslationsSaved?.(false, 'Failed to save to server');
          }
        } else {
          setSectionStatus(prev => ({
            ...prev,
            [sectionName]: { 
              type: 'success', 
              message: `${sectionName} section imported successfully! ${translationRows.length} items.` 
            }
          }));
        }

      } catch (error) {
        console.error(`Import error for ${sectionName}:`, error);
        setSectionStatus(prev => ({
          ...prev,
          [sectionName]: { type: 'error', message: 'Failed to process Excel file' }
        }));
      } finally {
        setProcessingSection(null);
        // Clear file input
        if (fileInputRefs.current[sectionName]) {
          fileInputRefs.current[sectionName]!.value = '';
        }
      }
    };

    reader.readAsArrayBuffer(file);
  };

  // Clear status for a section
  const clearSectionStatus = (sectionName: string) => {
    setSectionStatus(prev => ({
      ...prev,
      [sectionName]: { type: null, message: '' }
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
            <i className="ri-folder-line text-purple-600 text-xl"></i>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Section-wise Translation Management</h3>
            <p className="text-sm text-gray-600">Manage translations by individual sections with dedicated download/upload for each</p>
          </div>
        </div>

        {/* Auto-save Settings */}
        {!readOnly && languageId && (
          <div className="flex items-center justify-between bg-white rounded-lg p-3 border border-gray-200">
            <div className="flex items-center space-x-3">
              <i className="ri-save-line text-blue-500 text-lg"></i>
              <div>
                <h4 className="font-medium text-gray-900">Auto-save to Server</h4>
                <p className="text-sm text-gray-600">Automatically save section changes to the backend</p>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoSave}
                onChange={(e) => setAutoSave(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        )}
      </div>

      {/* Sections List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sections.map((sectionName) => {
          const sectionData = value[sectionName];
          const itemCount = typeof sectionData === 'object' && sectionData !== null 
            ? Object.keys(sectionData).length 
            : 0;
          const status = sectionStatus[sectionName];
          const isProcessing = processingSection === sectionName;

          return (
            <div key={sectionName} className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
              {/* Section Header */}
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 capitalize">{sectionName}</h3>
                  <p className="text-sm text-gray-600">{itemCount} translation items</p>
                </div>
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <i className="ri-folder-2-line text-purple-600"></i>
                </div>
              </div>

              {/* Section Status */}
              {status && status.type && (
                <div className={`mb-4 p-3 rounded-lg border text-sm ${
                  status.type === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
                  status.type === 'error' ? 'bg-red-50 border-red-200 text-red-800' :
                  'bg-blue-50 border-blue-200 text-blue-800'
                }`}>
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-2">
                      <i className={`${
                        status.type === 'success' ? 'ri-check-circle-line' :
                        status.type === 'error' ? 'ri-error-warning-line' :
                        'ri-information-line'
                      } text-lg mt-0.5`}></i>
                      <p>{status.message}</p>
                    </div>
                    <button
                      onClick={() => clearSectionStatus(sectionName)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <i className="ri-close-line"></i>
                    </button>
                  </div>
                </div>
              )}

              {/* Section Actions */}
              <div className="space-y-3">
                {/* Download Button */}
                <button
                  onClick={() => exportSection(sectionName)}
                  disabled={isProcessing || itemCount === 0}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-900 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <i className="ri-download-line text-lg"></i>
                  <span className="font-medium">
                    {isProcessing ? 'Exporting...' : `Download ${sectionName}`}
                  </span>
                </button>

                {/* Upload Button */}
                {!readOnly && (
                  <div>
                    <input
                      ref={(el) => fileInputRefs.current[sectionName] = el}
                      type="file"
                      accept=".xlsx,.xls"
                      onChange={(e) => handleSectionUpload(sectionName, e)}
                      disabled={isProcessing}
                      className="hidden"
                    />
                    <button
                      onClick={() => fileInputRefs.current[sectionName]?.click()}
                      disabled={isProcessing}
                      className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <i className="ri-upload-line text-lg"></i>
                      <span className="font-medium">
                        {isProcessing ? 'Processing...' : `Upload ${sectionName}`}
                      </span>
                    </button>
                  </div>
                )}
              </div>

              {/* Section Preview */}
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Preview:</h4>
                <div className="text-xs text-gray-600 space-y-1 max-h-20 overflow-y-auto">
                  {typeof sectionData === 'object' && sectionData !== null && 
                    Object.entries(sectionData).slice(0, 3).map(([key, value]) => (
                      <div key={key} className="flex justify-between">
                        <span className="font-mono">{key}:</span>
                        <span className="truncate ml-2">{String(value)}</span>
                      </div>
                    ))
                  }
                  {itemCount > 3 && (
                    <div className="text-gray-500 italic">
                      ... and {itemCount - 3} more items
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Instructions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="font-medium text-gray-900 mb-3">How to Use Section-wise Management:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h5 className="font-medium text-gray-800 mb-2">📥 Download Section:</h5>
            <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
              <li>Click "Download [section]" button for any section</li>
              <li>Excel file will be downloaded with that section's translations</li>
              <li>File name: [LanguageName]_[SECTION]_[Date].xlsx</li>
            </ol>
          </div>
          <div>
            <h5 className="font-medium text-gray-800 mb-2">📤 Upload Section:</h5>
            <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
              <li>Edit the downloaded Excel file</li>
              <li>Click "Upload [section]" button for that section</li>
              <li>Select your modified Excel file</li>
              <li>Changes will be applied automatically</li>
            </ol>
          </div>
        </div>
        
        <div className="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg">
          <p className="text-purple-800 text-sm">
            <strong>Perfect for:</strong> Individual section management, focused editing, team collaboration where each person handles specific sections.
          </p>
        </div>
      </div>
    </div>
  );
}
