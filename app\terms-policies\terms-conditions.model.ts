// Terms and Conditions Models and Types

export interface TermsConditions {
  id: number;
  terms: string;
  language: string;
  language_code: string;
  created_at: string;
  updated_at: string;
}

export interface TermsConditionsResponse {
  success: boolean;
  data: TermsConditions | TermsConditions[];
  message?: string;
  total?: number;
}

export interface CreateTermsConditionsRequest {
  terms: string;
  language: string;
  language_code: string;
}

export interface UpdateTermsConditionsRequest {
  terms?: string;
  language?: string;
  language_code?: string;
}

export interface TermsConditionsFormData {
  terms: string;
  language: string;
  language_code: string;
}

export interface TermsConditionsFilters {
  search: string;
  language: string;
  language_code: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

// Form validation errors
export interface TermsConditionsFormErrors {
  terms?: string;
  language?: string;
  language_code?: string;
}

// API response for single terms and conditions
export interface SingleTermsConditionsResponse {
  success: boolean;
  data: TermsConditions;
  message?: string;
}

// API response for multiple terms and conditions
export interface MultipleTermsConditionsResponse {
  success: boolean;
  data: TermsConditions[];
  total: number;
  message?: string;
}

// Language options for dropdown
export interface LanguageOption {
  label: string;
  value: string;
  code: string;
}

// Common language options
export const LANGUAGE_OPTIONS: LanguageOption[] = [
  { label: 'English', value: 'English', code: 'en' },
  { label: 'Spanish', value: 'Spanish', code: 'es' },
  { label: 'French', value: 'French', code: 'fr' },
  { label: 'German', value: 'German', code: 'de' },
  { label: 'Italian', value: 'Italian', code: 'it' },
  { label: 'Portuguese', value: 'Portuguese', code: 'pt' },
  { label: 'Russian', value: 'Russian', code: 'ru' },
  { label: 'Chinese', value: 'Chinese', code: 'zh' },
  { label: 'Japanese', value: 'Japanese', code: 'ja' },
  { label: 'Korean', value: 'Korean', code: 'ko' },
  { label: 'Hindi', value: 'Hindi', code: 'hi' },
  { label: 'Arabic', value: 'Arabic', code: 'ar' },
];

// Utility functions
export const getLanguageByCode = (code: string): LanguageOption | undefined => {
  return LANGUAGE_OPTIONS.find(lang => lang.code === code);
};

export const getLanguageByName = (name: string): LanguageOption | undefined => {
  return LANGUAGE_OPTIONS.find(lang => lang.value === name);
};

// Default form data
export const getDefaultTermsConditionsFormData = (): TermsConditionsFormData => ({
  terms: '',
  language: '',
  language_code: '',
});

// Validation functions
export const validateTermsConditionsForm = (data: TermsConditionsFormData): TermsConditionsFormErrors => {
  const errors: TermsConditionsFormErrors = {};

  if (!data.terms.trim()) {
    errors.terms = 'Terms and conditions content is required';
  } else if (data.terms.trim().length < 10) {
    errors.terms = 'Terms and conditions must be at least 10 characters long';
  }

  if (!data.language.trim()) {
    errors.language = 'Language is required';
  }

  if (!data.language_code.trim()) {
    errors.language_code = 'Language code is required';
  } else if (data.language_code.length < 2 || data.language_code.length > 3) {
    errors.language_code = 'Language code must be 2-3 characters long';
  }

  return errors;
};

// Format date utility
export const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch {
    return dateString;
  }
};
