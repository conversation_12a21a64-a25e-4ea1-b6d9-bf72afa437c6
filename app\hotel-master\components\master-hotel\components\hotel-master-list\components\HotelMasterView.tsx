'use client';

import { hotelDetail } from '@/app/hotel-master/hotel-master.model';
import React from 'react';
import Image from 'next/image';
import TabbedModal, { TabDefinition } from '@/app/components/ui/ModalWithTabs';

interface HotelViewProps {
  hotel: hotelDetail | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (hotelId: string) => void;
  loading?: boolean;
}

// Moved outside the component to prevent re-creating on every render
const tabs: TabDefinition[] = [
  { id: 'overview', label: 'Overview', icon: 'ri-information-line' },
  { id: 'facilities', label: 'Facilities', icon: 'ri-star-line' },
  { id: 'policies', label: 'Policies', icon: 'ri-file-text-line' },
  { id: 'reviews', label: 'Reviews', icon: 'ri-star-line' },
  { id: 'location', label: 'Location', icon: 'ri-map-pin-line' },
  { id: 'gallery', label: 'Gallery', icon: 'ri-image-line' },
];

export default function HotelMasterView({ hotel, isOpen, onClose, onEdit }: HotelViewProps) {
  if (!hotel) return null;

  const getStatusBadge = () => {
    const config = hotel.isVisible ? 
      { color: 'bg-green-100 text-green-800', icon: 'ri-check-line', label: 'Active' } :
      { color: 'bg-red-100 text-red-800', icon: 'ri-close-line', label: 'Inactive' };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <i className={`${config.icon} mr-1`}></i>
        {config.label}
      </span>
    );
  };

  const headerActions = (
    <div className="flex items-center space-x-3">
      {getStatusBadge()}
      <div className="flex items-center space-x-1 px-3 py-1 bg-amber-50 rounded-lg border border-amber-200">
        <div className="flex items-center space-x-1">
          {[...Array(5)].map((_, i) => {
            const rating = parseInt(hotel.starRating || '0');
            return <i key={i} className={`ri-star-${i < rating ? 'fill' : 'line'} text-amber-500 text-sm`}></i>;
          })}
        </div>
        <span className="text-sm font-medium text-amber-700 ml-1">
          {hotel.starRating || 'N/A'} Star{(parseInt(hotel.starRating || '0')) !== 1 ? 's' : ''}
        </span>
      </div>
      {onEdit && (
        <button
          onClick={() => onEdit(hotel.hotel_id)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
        >
          <i className="ri-edit-line mr-2 text-sm"></i>
          Edit Hotel
        </button>
      )}
    </div>
  );

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={hotel.name || 'Unnamed Hotel'}
      subtitle={`${hotel.city || 'Unknown City'}, ${hotel.country || 'Unknown Country'} • ${hotel.HotelType || 'Hotel'}`}
      tabs={tabs}
      size="full"
      headerActions={headerActions}
    >
      <TabbedModal.Content tabId="overview">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3"><i className="ri-building-line text-blue-600 text-lg"></i></div>
                <h3 className="text-lg font-semibold text-gray-900">Hotel Information</h3>
              </div>
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Hotel Name</label><p className="text-gray-900 font-medium">{hotel.name || 'Unnamed Hotel'}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Location</label><p className="text-gray-900">{hotel.address || `${hotel.city}, ${hotel.country}`}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Hotel Type</label><p className="text-gray-900">{hotel.HotelType || 'N/A'}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Category</label><p className="text-gray-900">{hotel.category || 'N/A'}</p></div>
                </div>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500 mb-1">Star Rating</label>
                    <div className="flex items-center space-x-1">
                      {[...Array(5)].map((_, i) => <i key={i} className={`ri-star-${i < parseInt(hotel.starRating || '0') ? 'fill' : 'line'} text-amber-400`}></i>)}
                      <span className="ml-2 text-gray-600">({hotel.starRating || 'N/A'}/5)</span>
                    </div>
                  </div>
                  {hotel.userRating && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500 mb-1">User Rating</label>
                      <p className="text-gray-900">{hotel.userRating} {hotel.userRatingCategoty && `(${hotel.userRatingCategoty})`}</p>
                    </div>
                  )}
                  {hotel.provider_name && <div><label className="block text-sm font-medium text-gray-500 mb-1">Provider</label><span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">{hotel.provider_name}</span></div>}
                  {hotel.hotel_id && <div><label className="block text-sm font-medium text-gray-500 mb-1">Hotel ID</label><p className="text-gray-900 font-mono text-sm">{hotel.hotel_id}</p></div>}
                </div>
              </div>
            </div>

            {/* About Section */}
            {hotel.about && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-information-line text-purple-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">About</h3>
                </div>
                <p className="text-gray-700 leading-relaxed">{hotel.about}</p>
              </div>
            )}
          </div>
          <div className="space-y-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact</h3>
              <div className="space-y-4">
                {hotel.phones_json && hotel.phones_json.length > 0 && <div className="flex justify-between items-center"><span className="text-gray-600">Phone</span><span className="font-semibold text-gray-900">{hotel.phones_json[0]}</span></div>}
                {hotel.faxes_json && hotel.faxes_json.length > 0 && <div className="flex justify-between items-center"><span className="text-gray-600">Fax</span><span className="font-semibold text-gray-900">{hotel.faxes_json[0]}</span></div>}
                <div className="flex justify-between items-center"><span className="text-gray-600">Last Updated</span><span className="font-semibold text-gray-900">{hotel.data_source_updated_at ? new Date(hotel.data_source_updated_at).toLocaleDateString() : 'N/A'}</span></div>
              </div>
            </div>

            {/* Room Details */}
            {hotel.roomDetails && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Room Details</h3>
                <div className="space-y-3">
                  {hotel.roomDetails.type && <div className="flex justify-between items-center"><span className="text-gray-600">Type</span><span className="font-semibold text-gray-900">{hotel.roomDetails.type}</span></div>}
                  {hotel.roomDetails.bedroom && <div className="flex justify-between items-center"><span className="text-gray-600">Bedroom</span><span className="font-semibold text-gray-900">{hotel.roomDetails.bedroom}</span></div>}
                  {hotel.roomDetails.bathroom && <div className="flex justify-between items-center"><span className="text-gray-600">Bathroom</span><span className="font-semibold text-gray-900">{hotel.roomDetails.bathroom}</span></div>}
                  {hotel.roomDetails.livingRoom && <div className="flex justify-between items-center"><span className="text-gray-600">Living Room</span><span className="font-semibold text-gray-900">{hotel.roomDetails.livingRoom}</span></div>}
                  {hotel.roomDetails.size && <div className="flex justify-between items-center"><span className="text-gray-600">Size</span><span className="font-semibold text-gray-900">{hotel.roomDetails.size}</span></div>}
                  {hotel.roomDetails.bed && <div className="flex justify-between items-center"><span className="text-gray-600">Bed</span><span className="font-semibold text-gray-900">{hotel.roomDetails.bed}</span></div>}
                </div>
              </div>
            )}

            {/* Amenities */}
            {hotel.amenities && hotel.amenities.length > 0 && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Special Amenities</h3>
                <div className="space-y-2">
                  {hotel.amenities.map((amenity, index) => (
                    <div key={index} className="flex items-center">
                      <i className="ri-check-line text-green-600 mr-2"></i>
                      <span className="text-sm text-gray-700">{amenity}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Pricing */}
            {hotel.recent_pricing && hotel.recent_pricing.length > 0 && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-price-tag-3-line text-green-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Recent Pricing</h3>
                </div>
                <div className="space-y-3">
                  {hotel.recent_pricing.map((pricing) => (
                    <div key={pricing.id} className="border border-gray-200 rounded-lg p-4 bg-gradient-to-r from-green-50 to-white">
                      <div className="flex justify-between items-start mb-2">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {pricing.provider_name}
                        </span>
                        {pricing.refundable && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            <i className="ri-check-line mr-1"></i>
                            Refundable
                          </span>
                        )}
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Base Rate</span>
                          <span className="font-semibold text-gray-900">{pricing.currency_code} {pricing.base_rate.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">Total Rate</span>
                          <span className="font-bold text-green-600 text-lg">{pricing.currency_code} {pricing.total_rate.toFixed(2)}</span>
                        </div>
                        {pricing.board_basis && (
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Board Basis</span>
                            <span className="text-sm text-gray-900 capitalize">{pricing.board_basis.replace('_', ' ')}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="facilities">
        <div className="space-y-6">
          {/* Descriptions */}
          {hotel.descriptions && hotel.descriptions.length > 0 && (
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Descriptions</h3>
              <div className="space-y-4">
                {hotel.descriptions.map((desc) => (
                  <div key={desc.id} className="border-l-4 border-blue-500 pl-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-gray-900 capitalize">{desc.description_type.replace('_', ' ')}</h4>
                      <span className="text-xs text-gray-500 uppercase">{desc.language}</span>
                    </div>
                    {desc.content.title && <p className="font-medium text-gray-800 mb-2">{desc.content.title}</p>}
                    {desc.content.description && <p className="text-gray-700 text-sm leading-relaxed mb-3">{desc.content.description}</p>}
                    {desc.content.highlights && desc.content.highlights.length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-2">Highlights:</p>
                        <ul className="list-disc list-inside space-y-1">
                          {desc.content.highlights.map((highlight, idx) => (
                            <li key={idx} className="text-sm text-gray-600">{highlight}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                    {desc.content.amenities && desc.content.amenities.length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-2">Amenities:</p>
                        <div className="flex flex-wrap gap-2">
                          {desc.content.amenities.map((amenity, idx) => (
                            <span key={idx} className="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 rounded text-xs">
                              <i className="ri-check-line mr-1"></i>
                              {amenity}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {desc.content.nearby && desc.content.nearby.length > 0 && (
                      <div>
                        <p className="text-sm font-medium text-gray-700 mb-2">Nearby:</p>
                        <ul className="list-disc list-inside space-y-1">
                          {desc.content.nearby.map((place, idx) => (
                            <li key={idx} className="text-sm text-gray-600">{place}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Facilities */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Facilities & Amenities</h3>
            {(hotel.facilities && hotel.facilities.length > 0) ? (
              <div className="space-y-4">
                {hotel.facilities.map((facility) => (
                  <div key={facility.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                    <div className="flex items-center mb-2">
                      <i className="ri-check-circle-fill text-green-600 mr-2"></i>
                      <span className="font-semibold text-gray-900">{facility.name || 'Unnamed Facility'}</span>
                    </div>
                    {facility.details && facility.details.length > 0 && (
                      <div className="ml-6 space-y-1">
                        {facility.details.map((detail) => (
                          <p key={detail.id} className="text-sm text-gray-600">{detail.content}</p>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (<div className="text-center py-8"><p className="text-gray-500 italic">No facilities information available</p></div>)}
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="policies">
        <div className="space-y-6">
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
             <h3 className="text-lg font-semibold text-gray-900 mb-4">Policies</h3>
             {(hotel.policies && hotel.policies.length > 0) ? (
              <div className="space-y-4">
                {hotel.policies.map(policy => (
                  <div key={policy.id}><strong className="capitalize">{policy.name?.replace('_', ' ')}:</strong> {policy.description}</div>
                ))}
              </div>
             ) : (<div className="text-center py-8"><p className="text-gray-500 italic">No policy information available</p></div>)}
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="reviews">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Reviews</h3>
          {(hotel.reviews && hotel.reviews.length > 0) ? (
            <div className="space-y-4">
              {hotel.reviews.map((review) => (
                <div key={review.id} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <i key={i} className={`ri-star-${i < review.rating ? 'fill' : 'line'} text-amber-400 text-sm`}></i>
                        ))}
                      </div>
                      <span className="font-semibold text-gray-900">{review.rating}/5</span>
                    </div>
                    {review.type && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                        {review.type}
                      </span>
                    )}
                  </div>
                  <p className="text-gray-700 text-sm leading-relaxed">{review.content}</p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <i className="ri-chat-3-line text-gray-300 text-5xl mb-3"></i>
              <p className="text-gray-500 italic">No reviews available</p>
            </div>
          )}
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="location">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Location Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div><label className="block text-sm font-medium text-gray-500 mb-1">Country</label><p className="text-gray-900 font-medium">{hotel.country}</p></div>
              <div><label className="block text-sm font-medium text-gray-500 mb-1">City</label><p className="text-gray-900 font-medium">{hotel.city}</p></div>
              <div><label className="block text-sm font-medium text-gray-500 mb-1">State</label><p className="text-gray-900 font-medium">{hotel.stateName || 'N/A'}</p></div>
            </div>
            <div className="space-y-4">
              {hotel.geoLocationInfo && (
                <>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Latitude</label><p className="text-gray-900 font-mono">{hotel.geoLocationInfo.lat}</p></div>
                  <div><label className="block text-sm font-medium text-gray-500 mb-1">Longitude</label><p className="text-gray-900 font-mono">{hotel.geoLocationInfo.lon}</p></div>
                </>
              )}
            </div>
          </div>
        </div>
      </TabbedModal.Content>

      <TabbedModal.Content tabId="gallery">
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Gallery {hotel.images && hotel.images.length > 0 && (
              <span className="text-sm font-normal text-gray-500 ml-2">({hotel.images.length} images)</span>
            )}
          </h3>
          {(hotel.images && hotel.images.length > 0) ? (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {hotel.images.map((image) => (
                <div key={image.id} className="group relative overflow-hidden rounded-lg border border-gray-200 hover:border-blue-400 transition-all duration-200 hover:shadow-lg">
                  <div className="relative w-full h-48">
                    <Image
                      src={image.image_path}
                      alt={image.alt_text || 'Hotel Image'}
                      fill
                      sizes="(max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                      className="object-cover group-hover:scale-105 transition-transform duration-200"
                      unoptimized={true}
                    />
                    {image.is_hero_image && (
                      <div className="absolute top-2 right-2 bg-amber-500 text-white px-2 py-1 rounded-md text-xs font-semibold flex items-center">
                        <i className="ri-star-fill mr-1"></i>
                        Hero
                      </div>
                    )}
                    {image.image_category_type && (
                      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2">
                        <p className="text-white text-xs font-medium">{image.image_category_type}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (<div className="text-center py-8"><p className="text-gray-500 italic">No gallery images available</p></div>)}
        </div>
      </TabbedModal.Content>
    </TabbedModal>
  );
}