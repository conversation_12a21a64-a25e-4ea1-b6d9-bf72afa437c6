'use client';

import React from 'react';
import TabbedModal from '../../../components/ui/TabbedModal';
import { PrivacyPolicies, formatDate } from '../../privacy-policies.model';

interface PrivacyPoliciesViewProps {
  privacyPolicies: PrivacyPolicies | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (privacyPolicies: PrivacyPolicies) => void;
}

export default function PrivacyPoliciesView({ 
  privacyPolicies, 
  isOpen, 
  onClose, 
  onEdit 
}: PrivacyPoliciesViewProps) {
  if (!privacyPolicies) return null;

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="space-y-8">
          {/* Header Information */}
          <div className="bg-gray-50 rounded-lg p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">ID</label>
                <p className="text-lg font-semibold text-gray-900">{privacyPolicies.id}</p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Language</label>
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-2">
                    <i className="ri-shield-user-line text-green-600 text-xs"></i>
                  </div>
                  <p className="text-lg font-semibold text-gray-900">{privacyPolicies.language}</p>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Language Code</label>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                  {privacyPolicies.language_code.toUpperCase()}
                </span>
              </div>
            </div>
          </div>

          {/* Timestamps */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-calendar-check-line text-green-600"></i>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Created At</p>
                  <p className="text-lg font-semibold text-gray-900">{formatDate(privacyPolicies.created_at)}</p>
                </div>
              </div>
            </div>

            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-calendar-event-line text-blue-600"></i>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Last Updated</p>
                  <p className="text-lg font-semibold text-gray-900">{formatDate(privacyPolicies.updated_at)}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Content Statistics */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Content Statistics</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{privacyPolicies.privacy_policy.length}</div>
                <div className="text-sm text-gray-500">Characters</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{privacyPolicies.privacy_policy.split(' ').length}</div>
                <div className="text-sm text-gray-500">Words</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{privacyPolicies.privacy_policy.split('\n').length}</div>
                <div className="text-sm text-gray-500">Lines</div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'content',
      label: 'Full Content',
      icon: 'ri-shield-user-line',
      content: (
        <div className="space-y-6">
          {/* Content Header */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Privacy Policy Content</h3>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Language:</span>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {privacyPolicies.language} ({privacyPolicies.language_code.toUpperCase()})
              </span>
            </div>
          </div>

          {/* Content Display */}
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div className="prose max-w-none">
              <div className="text-gray-800 whitespace-pre-wrap leading-relaxed">
                {privacyPolicies.privacy_policy}
              </div>
            </div>
          </div>

          {/* Content Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              onClick={() => navigator.clipboard.writeText(privacyPolicies.privacy_policy)}
              className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              <i className="ri-clipboard-line mr-2"></i>
              Copy Content
            </button>
            <button
              onClick={() => {
                const blob = new Blob([privacyPolicies.privacy_policy], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `privacy-policy-${privacyPolicies.language_code}.txt`;
                a.click();
                URL.revokeObjectURL(url);
              }}
              className="inline-flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              <i className="ri-download-line mr-2"></i>
              Download
            </button>
          </div>
        </div>
      )
    }
  ];

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Privacy Policy - ${privacyPolicies.language}`}
      subtitle={`Language Code: ${privacyPolicies.language_code.toUpperCase()} • ID: ${privacyPolicies.id}`}
      tabs={tabs}
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-3">
          {/* Edit Button */}
          {onEdit && (
            <button
              onClick={() => onEdit(privacyPolicies)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
            >
              <i className="ri-edit-line mr-2 text-sm"></i>
              Edit Privacy Policy
            </button>
          )}
        </div>
      }
    />
  );
}
