'use client';

import React from 'react';
import TabbedModal from '../../../components/ui/TabbedModal';
import { Booking, BookingStatus, PaymentStatus } from '../types';

interface BookingViewProps {
  booking: Booking | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (booking: Booking) => void;
}

export default function BookingView({ booking, isOpen, onClose, onEdit }: BookingViewProps) {
  if (!booking) return null;

  const getBookingStatusBadge = (status: BookingStatus) => {
    const statusConfig = {
      confirmed: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', icon: 'ri-check-line' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', icon: 'ri-time-line' },
      'checked-in': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', icon: 'ri-login-box-line' },
      'checked-out': { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', icon: 'ri-logout-box-line' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', icon: 'ri-close-line' },
      'no-show': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', icon: 'ri-user-unfollow-line' },
      completed: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', icon: 'ri-checkbox-circle-line' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium ${config.bg} ${config.text} border ${config.border}`}>
        <i className={`${config.icon} mr-2`}></i>
        {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
      </span>
    );
  };

  const getPaymentStatusBadge = (status: PaymentStatus) => {
    const statusConfig = {
      paid: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', icon: 'ri-check-line' },
      partial: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', icon: 'ri-pie-chart-line' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', icon: 'ri-time-line' },
      failed: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', icon: 'ri-close-line' },
      refunded: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', icon: 'ri-refund-line' },
      cancelled: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', icon: 'ri-close-circle-line' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-flex items-center px-3 py-1.5 rounded-lg text-sm font-medium ${config.bg} ${config.text} border ${config.border}`}>
        <i className={`${config.icon} mr-2`}></i>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const calculateNights = () => {
    const checkIn = new Date(booking.checkInDate);
    const checkOut = new Date(booking.checkOutDate);
    const diffTime = Math.abs(checkOut.getTime() - checkIn.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="space-y-6">
          {/* Booking Header */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6 border border-blue-100">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{booking.bookingId}</h3>
                <p className="text-gray-600">{booking.hotelName}</p>
              </div>
              <div className="text-right">
                {getBookingStatusBadge(booking.bookingStatus)}
              </div>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-500">Check-in</p>
                <p className="font-semibold">{new Date(booking.checkInDate).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-gray-500">Check-out</p>
                <p className="font-semibold">{new Date(booking.checkOutDate).toLocaleDateString()}</p>
              </div>
              <div>
                <p className="text-gray-500">Nights</p>
                <p className="font-semibold">{calculateNights()}</p>
              </div>
              <div>
                <p className="text-gray-500">Guests</p>
                <p className="font-semibold">{booking.numberOfGuests}</p>
              </div>
            </div>
          </div>

          {/* Guest Information */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i className="ri-user-line mr-2 text-blue-600"></i>
              Guest Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500 mb-1">Name</p>
                <p className="font-medium text-gray-900">{booking.guestName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Email</p>
                <p className="font-medium text-gray-900">{booking.guestEmail}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Phone</p>
                <p className="font-medium text-gray-900">{booking.guestPhone}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Booking Source</p>
                <p className="font-medium text-gray-900 capitalize">{booking.bookingSource.replace('-', ' ')}</p>
              </div>
            </div>
          </div>

          {/* Room Information */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i className="ri-home-4-line mr-2 text-blue-600"></i>
              Room Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500 mb-1">Room Type</p>
                <p className="font-medium text-gray-900">{booking.roomName}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Hotel</p>
                <p className="font-medium text-gray-900">{booking.hotelName}</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'payment',
      label: 'Payment',
      icon: 'ri-money-dollar-circle-line',
      content: (
        <div className="space-y-6">
          {/* Payment Status */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h4 className="text-lg font-semibold text-gray-900 flex items-center">
                <i className="ri-money-dollar-circle-line mr-2 text-green-600"></i>
                Payment Information
              </h4>
              {getPaymentStatusBadge(booking.paymentStatus)}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500 mb-1">Total Amount</p>
                <p className="text-2xl font-bold text-gray-900">{booking.currency} {booking.totalAmount.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Amount Paid</p>
                <p className="text-2xl font-bold text-green-600">{booking.currency} {booking.amountPaid.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Amount Due</p>
                <p className="text-2xl font-bold text-red-600">{booking.currency} {booking.amountDue.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500 mb-1">Payment Method</p>
                <p className="font-medium text-gray-900 capitalize">{booking.paymentMethod.replace('-', ' ')}</p>
              </div>
            </div>

            {/* Payment Progress */}
            <div className="mt-6">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Payment Progress</span>
                <span>{Math.round((booking.amountPaid / booking.totalAmount) * 100)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(booking.amountPaid / booking.totalAmount) * 100}%` }}
                ></div>
              </div>
            </div>
          </div>

          {/* Payment Details */}
          {booking.paymentDetails && (
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i className="ri-secure-payment-line mr-2 text-blue-600"></i>
                Payment Details
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {booking.paymentDetails.transactionId && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Transaction ID</p>
                    <p className="font-medium text-gray-900 font-mono">{booking.paymentDetails.transactionId}</p>
                  </div>
                )}
                {booking.paymentDetails.paymentGateway && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Payment Gateway</p>
                    <p className="font-medium text-gray-900">{booking.paymentDetails.paymentGateway}</p>
                  </div>
                )}
                {booking.paymentDetails.cardLast4 && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Card</p>
                    <p className="font-medium text-gray-900">**** **** **** {booking.paymentDetails.cardLast4}</p>
                  </div>
                )}
                {booking.paymentDetails.paymentDate && (
                  <div>
                    <p className="text-sm text-gray-500 mb-1">Payment Date</p>
                    <p className="font-medium text-gray-900">{new Date(booking.paymentDetails.paymentDate).toLocaleDateString()}</p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )
    },
    {
      id: 'details',
      label: 'Details',
      icon: 'ri-file-text-line',
      content: (
        <div className="space-y-6">
          {/* Booking Timeline */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i className="ri-time-line mr-2 text-blue-600"></i>
              Booking Timeline
            </h4>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-blue-600 rounded-full mr-4"></div>
                <div>
                  <p className="font-medium text-gray-900">Booking Created</p>
                  <p className="text-sm text-gray-500">{new Date(booking.bookingDate).toLocaleString()}</p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-600 rounded-full mr-4"></div>
                <div>
                  <p className="font-medium text-gray-900">Last Updated</p>
                  <p className="text-sm text-gray-500">{new Date(booking.updatedAt).toLocaleString()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Special Requests */}
          {booking.specialRequests && (
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i className="ri-message-3-line mr-2 text-blue-600"></i>
                Special Requests
              </h4>
              <p className="text-gray-700 whitespace-pre-wrap">{booking.specialRequests}</p>
            </div>
          )}

          {/* Notes */}
          {booking.notes && (
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <i className="ri-sticky-note-line mr-2 text-blue-600"></i>
                Internal Notes
              </h4>
              <p className="text-gray-700 whitespace-pre-wrap">{booking.notes}</p>
            </div>
          )}

          {/* Confirmation Details */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <i className="ri-shield-check-line mr-2 text-blue-600"></i>
              Confirmation Details
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm text-gray-500 mb-1">Booking ID</p>
                <p className="font-medium text-gray-900 font-mono">{booking.bookingId}</p>
              </div>
              {booking.confirmationNumber && (
                <div>
                  <p className="text-sm text-gray-500 mb-1">Confirmation Number</p>
                  <p className="font-medium text-gray-900 font-mono">{booking.confirmationNumber}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`Booking Details - ${booking.bookingId}`}
      subtitle={`${booking.guestName} • ${booking.hotelName}`}
      tabs={tabs}
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-3">
          {/* Edit Button */}
          {onEdit && (
            <button
              onClick={() => onEdit(booking)}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
            >
              <i className="ri-edit-line mr-2 text-sm"></i>
              Edit Booking
            </button>
          )}
        </div>
      }
    />
  );
}
