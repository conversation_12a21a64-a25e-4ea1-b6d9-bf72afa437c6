'use client';

import { useState, useMemo } from 'react';
import { Booking, BookingStatus, PaymentStatus, PaymentMethod } from '../types';
import { usePaginationState } from '../../../hooks/usePagination';
import Pagination from '../../../styles/components/Pagination';

interface BookingListProps {
  bookings: Booking[];
  onEdit: (booking: Booking) => void;
  onView: (booking: Booking) => void;
  onDelete: (booking: Booking) => void;
}

export default function BookingList({ bookings, onEdit, onView, onDelete }: BookingListProps) {
  const [sortField, setSortField] = useState<keyof Booking>('bookingDate');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Memoize sorted bookings to prevent unnecessary re-renders
  const sortedBookings = useMemo(() => {
    return [...bookings].sort((a, b) => {
      const aValue = a[sortField];
      const bValue = b[sortField];

      if (aValue === undefined && bValue === undefined) return 0;
      if (aValue === undefined) return 1;
      if (bValue === undefined) return -1;

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });
  }, [bookings, sortField, sortDirection]);

  // Use pagination state hook to avoid infinite re-renders
  const paginationState = usePaginationState({ initialItemsPerPage: 5 });
  const { currentPage, itemsPerPage, setCurrentPage, setItemsPerPage } = paginationState;

  // Calculate pagination info
  const paginationInfo = paginationState.getPaginationInfo(sortedBookings.length);
  const { totalPages, startIndex, endIndex } = paginationInfo;

  // Get paginated data
  const paginatedData = useMemo(() => {
    return sortedBookings.slice(startIndex, endIndex);
  }, [sortedBookings, startIndex, endIndex]);

  const handleSort = (field: keyof Booking) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const getBookingStatusBadge = (status: BookingStatus) => {
    const statusConfig = {
      confirmed: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', icon: 'ri-check-line' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', icon: 'ri-time-line' },
      'checked-in': { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', icon: 'ri-login-box-line' },
      'checked-out': { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200', icon: 'ri-logout-box-line' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', icon: 'ri-close-line' },
      'no-show': { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', icon: 'ri-user-unfollow-line' },
      completed: { bg: 'bg-emerald-100', text: 'text-emerald-800', border: 'border-emerald-200', icon: 'ri-checkbox-circle-line' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-medium ${config.bg} ${config.text} border ${config.border}`}>
        <i className={`${config.icon} mr-1`}></i>
        {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
      </span>
    );
  };

  const getPaymentStatusBadge = (status: PaymentStatus) => {
    const statusConfig = {
      paid: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200', icon: 'ri-check-line' },
      partial: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200', icon: 'ri-pie-chart-line' },
      pending: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200', icon: 'ri-time-line' },
      failed: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200', icon: 'ri-close-line' },
      refunded: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200', icon: 'ri-refund-line' },
      cancelled: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200', icon: 'ri-close-circle-line' }
    };

    const config = statusConfig[status];
    return (
      <span className={`inline-flex items-center px-2.5 py-1 rounded-lg text-xs font-medium ${config.bg} ${config.text} border ${config.border}`}>
        <i className={`${config.icon} mr-1`}></i>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    );
  };

  const getPaymentMethodIcon = (method: PaymentMethod) => {
    const methodConfig = {
      'credit-card': 'ri-bank-card-line',
      'debit-card': 'ri-bank-card-2-line',
      'bank-transfer': 'ri-bank-line',
      cash: 'ri-money-dollar-circle-line',
      paypal: 'ri-paypal-line',
      stripe: 'ri-secure-payment-line',
      razorpay: 'ri-secure-payment-line',
      other: 'ri-more-line'
    };

    return methodConfig[method] || 'ri-more-line';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors">
                <div className="flex items-center"> Booking ID</div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors" onClick={() => handleSort('guestName')}>
                <div className="flex items-center">
                  Guest Name
                  {sortField === 'guestName' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Payment Status
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Booking Status
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors" onClick={() => handleSort('amountPaid')}>
                <div className="flex items-center">
                  Amount Paid {sortField === 'amountPaid' && ( <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i> )}
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Payment Method
              </th>
              <th scope="col" className="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors" onClick={() => handleSort('bookingDate')}>
                <div className="flex items-center">
                  Booking Date
                  {sortField === 'bookingDate' && (
                    <i className={`ri-arrow-${sortDirection === 'asc' ? 'up' : 'down'}-s-line ml-1`}></i>
                  )}
                </div>
              </th>
              <th scope="col" className="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paginatedData.map((booking: Booking) => (
              <tr key={booking.id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-semibold text-gray-900">{booking.bookingId || 'N/A'}</div>
                  {/* <div className="text-sm text-gray-500">{booking.hotelName || 'N/A'}</div> */}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {booking.guestName ? booking.guestName.split(' ').map((n: string) => n[0]).join('').slice(0, 2) : 'NA'}
                        </span>
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900">{booking.guestName || 'N/A'}</div>
                      <div className="text-sm text-gray-500">{booking.guestEmail || 'N/A'}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {booking.paymentStatus ? getPaymentStatusBadge(booking.paymentStatus) : <span className="text-sm text-gray-500">N/A</span>}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {booking.bookingStatus ? getBookingStatusBadge(booking.bookingStatus) : <span className="text-sm text-gray-500">N/A</span>}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-semibold text-gray-900">
                    {booking.currency || 'USD'} {booking.amountPaid ? booking.amountPaid.toLocaleString() : '0'}
                  </div>
                  <div className="text-xs text-gray-500">
                    of {booking.currency || 'USD'} {booking.totalAmount ? booking.totalAmount.toLocaleString() : '0'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <i className={`${booking.paymentMethod ? getPaymentMethodIcon(booking.paymentMethod) : 'ri-more-line'} mr-2 text-gray-400`}></i>
                    <span className="text-sm text-gray-900 capitalize">
                      {booking.paymentMethod ? booking.paymentMethod.replace('-', ' ') : 'N/A'}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {booking.bookingDate ? new Date(booking.bookingDate).toLocaleDateString() : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => onView(booking)}
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <i className="ri-eye-line"></i>
                    </button>
                    <button
                      onClick={() => onEdit(booking)}
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                      title="Edit Booking"
                    >
                      <i className="ri-edit-line"></i>
                    </button>
                    <button
                      onClick={() => onDelete(booking)}
                      className="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Delete Booking"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {bookings.length > 0 && (
        <div className="mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={itemsPerPage}
            totalItems={sortedBookings.length}
            onPageChange={(page: number) => setCurrentPage(page, totalPages)}
            onItemsPerPageChange={(items: number) => setItemsPerPage(items, sortedBookings.length)}
          />
        </div>
      )}
    </div>
  );
}
