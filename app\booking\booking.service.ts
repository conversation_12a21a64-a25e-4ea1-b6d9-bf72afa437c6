import apiService from "../api/api-service";
import axiosInstance from "../api/axiosInstance";
import { Booking, CreateBookingRequest, UpdateBookingRequest, BookingSearchFilters } from "./booking.model";
import { BookingApiResponse } from "./bookings.model";
import { Booking as ComponentBooking } from "./components/types";

// Get all bookings

const bookingBaseUrl = 'api/v1'

// Transform API response to ComponentBooking format
const transformApiResponseToBooking = (apiResponse: BookingApiResponse): ComponentBooking => {
  const hotelBooking = apiResponse.hotel_booking;
  const billingContact = hotelBooking.billing_contact;
  const firstGuest = hotelBooking.rooms_allocations[0]?.guests[0];
  const firstRate = hotelBooking.booking_snapshot?.rates[0];
  const firstRoom = hotelBooking.booking_snapshot?.rooms[0];

  return {
    id: apiResponse.id.toString(),
    bookingId: apiResponse.booking_reference,
    guestName: firstGuest ? `${firstGuest.firstname} ${firstGuest.lastname}` : `${billingContact.firstName} ${billingContact.lastName}`,
    guestEmail: firstGuest?.email || billingContact.contact.email,
    guestPhone: billingContact.contact.phone,
    hotelId: hotelBooking.hotel_id,
    hotelName: hotelBooking.booking_snapshot?.hotelName || 'Hotel Name Not Available',
    roomId: firstRoom?.id || '',
    roomName: firstRoom?.name || 'Room Name Not Available',
    checkInDate: new Date().toISOString().split('T')[0], // Default to today, adjust based on your data
    checkOutDate: new Date(Date.now() + 86400000).toISOString().split('T')[0], // Default to tomorrow
    numberOfGuests: hotelBooking.rooms_allocations.reduce((total, allocation) => total + allocation.guests.length, 0),
    numberOfNights: 1, // Calculate based on actual dates when available
    bookingStatus: mapApiStatusToBookingStatus(apiResponse.status),
    paymentStatus: mapApiPaymentStatusToPaymentStatus(apiResponse.payment_status),
    paymentMethod: 'credit-card' as const,
    totalAmount: firstRate?.totalRate || 0,
    amountPaid: firstRate?.totalRate || 0,
    amountDue: 0,
    currency: firstRate?.currency || 'USD',
    bookingDate: apiResponse.created_at,
    createdAt: apiResponse.created_at,
    updatedAt: apiResponse.updated_at,
    bookingSource: 'direct' as const,

    // Optional fields
    specialRequests: hotelBooking.special_requests || undefined,
    guestDetails: {
      firstName: firstGuest?.firstname || billingContact.firstName,
      lastName: firstGuest?.lastname || billingContact.lastName,
      email: firstGuest?.email || billingContact.contact.email,
      phone: billingContact.contact.phone,
      address: {
        street: billingContact.contact.address.line1,
        city: billingContact.contact.address.city.name,
        state: billingContact.contact.address.state.name,
        country: billingContact.contact.address.country.name,
        zipCode: billingContact.contact.address.postalCode
      }
    }
  };
};

// Map API status to booking status
const mapApiStatusToBookingStatus = (apiStatus: string): any => {
  switch (apiStatus.toUpperCase()) {
    case 'PENDING':
      return 'pending';
    case 'SUCCESS':
      return 'confirmed';
    case 'FAILED':
      return 'cancelled';
    default:
      return 'pending';
  }
};

// Map API payment status to payment status
const mapApiPaymentStatusToPaymentStatus = (apiPaymentStatus: string): any => {
  switch (apiPaymentStatus.toUpperCase()) {
    case 'PAID':
      return 'paid';
    case 'UNPAID':
      return 'pending';
    default:
      return 'pending';
  }
};

export const getAllBookingApi = async (): Promise<ComponentBooking[]> => {
  try {
    const apiResponse = await apiService.getbooking<BookingApiResponse[]>(`${bookingBaseUrl}/booking`);
    return apiResponse.map(transformApiResponseToBooking);
  } catch (error) {
    console.error('Error fetching bookings:', error);
    // Return mock data as fallback
    return getMockComponentBookings();
  }
}

// Get booking by ID
export const getBookingById = async (id: string, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.getbooking<Booking>(`/bookings/${id}/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching booking by ID:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getBookingById(id, retries - 1);
    }

    // Return mock data as fallback
    const mockBookings = getMockBookings();
    const booking = mockBookings.find(b => b.id === id);
    if (!booking) {
      throw new Error(`Booking with ID ${id} not found`);
    }
    return booking;
  }
};

// Create new booking
export const createBooking = async (bookingData: CreateBookingRequest, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.postbooking<Booking>('/bookings/', bookingData);
    return response.data;
  } catch (error: any) {
    console.error('Error creating booking:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return createBooking(bookingData, retries - 1);
    }

    // Create mock booking as fallback
    const mockBooking: Booking = {
      id: Date.now().toString(),
      bookingReference: `BK${Date.now()}`,
      ...bookingData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return mockBooking;
  }
};

// Update booking
export const updateBooking = async (id: string, bookingData: UpdateBookingRequest, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.putbooking<Booking>(`/bookings/${id}/`, bookingData);
    return response.data;
  } catch (error: any) {
    console.error('Error updating booking:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateBooking(id, bookingData, retries - 1);
    }

    // Return updated mock booking as fallback
    const existingBooking = await getBookingById(id);
    const updatedBooking: Booking = {
      ...existingBooking,
      ...bookingData,
      updatedAt: new Date().toISOString(),
    };
    return updatedBooking;
  }
};

// Cancel booking
export const cancelBooking = async (id: string, reason?: string, retries = 3): Promise<Booking> => {
  try {
    const response = await apiService.postbooking<Booking>(`/bookings/${id}/cancel/`, { reason });
    return response.data;
  } catch (error: any) {
    console.error('Error cancelling booking:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return cancelBooking(id, reason, retries - 1);
    }

    // Return cancelled mock booking as fallback
    const existingBooking = await getBookingById(id);
    const cancelledBooking: Booking = {
      ...existingBooking,
      status: 'cancelled',
      cancellationReason: reason,
      cancelledAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    return cancelledBooking;
  }
};

// Update booking status
export const updateBookingStatus = async (id: string, status: Booking['status'], retries = 3): Promise<Booking> => {
  try {
    const response = await axiosInstance.patch<Booking>(`/bookings/${id}/status/`, { status });
    return response.data;
  } catch (error: any) {
    console.error('Error updating booking status:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return updateBookingStatus(id, status, retries - 1);
    }

    // Return updated mock booking as fallback
    const existingBooking = await getBookingById(id);
    const updatedBooking: Booking = {
      ...existingBooking,
      status,
      updatedAt: new Date().toISOString(),
    };
    return updatedBooking;
  }
};

// Search bookings
export const searchBookings = async (filters: BookingSearchFilters, retries = 3): Promise<Booking[]> => {
  try {
    const params = new URLSearchParams();
    Object.keys(filters).forEach(key => {
      const value = filters[key as keyof BookingSearchFilters];
      if (value) params.append(key, value.toString());
    });

    const response = await axiosInstance.get<Booking[]>(`/bookings/search/?${params.toString()}`);
    return response.data;
  } catch (error: any) {
    console.error('Error searching bookings:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return searchBookings(filters, retries - 1);
    }

    // Return filtered mock data as fallback
    const mockBookings = getMockBookings();
    return mockBookings.filter(booking => {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        if (!booking.bookingReference.toLowerCase().includes(searchLower) &&
            !booking.guestName.toLowerCase().includes(searchLower) &&
            !booking.hotelName.toLowerCase().includes(searchLower)) {
          return false;
        }
      }
      if (filters.status && booking.status !== filters.status) return false;
      if (filters.hotelId && booking.hotelId !== filters.hotelId) return false;
      if (filters.checkInFrom && booking.checkInDate < filters.checkInFrom) return false;
      if (filters.checkInTo && booking.checkInDate > filters.checkInTo) return false;
      return true;
    });
  }
};

// Get bookings by hotel
export const getBookingsByHotel = async (hotelId: string, retries = 3): Promise<Booking[]> => {
  try {
    const response = await axiosInstance.get<Booking[]>(`/hotels/${hotelId}/bookings/`);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching bookings by hotel:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getBookingsByHotel(hotelId, retries - 1);
    }

    // Return filtered mock data as fallback
    const mockBookings = getMockBookings();
    return mockBookings.filter(booking => booking.hotelId === hotelId);
  }
};

// Get booking statistics
export const getBookingStats = async (retries = 3): Promise<any> => {
  try {
    const response = await axiosInstance.get('/bookings/stats/');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching booking stats:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getBookingStats(retries - 1);
    }

    // Return mock stats as fallback
    const mockBookings = getMockBookings();
    return {
      totalBookings: mockBookings.length,
      confirmedBookings: mockBookings.filter(b => b.status === 'confirmed').length,
      cancelledBookings: mockBookings.filter(b => b.status === 'cancelled').length,
      totalRevenue: mockBookings.reduce((sum, b) => sum + b.totalAmount, 0)
    };
  }
};

// Mock data for fallback
const getMockBookings = (): Booking[] => [
  {
    id: '1',
    bookingReference: 'BK2024001',
    hotelId: '1',
    hotelName: 'Grand Palace Resort & Spa',
    roomId: '1',
    roomName: 'Deluxe Ocean View',
    guestName: 'John Smith',
    guestEmail: '<EMAIL>',
    guestPhone: '****** 123 4567',
    checkInDate: '2024-02-15',
    checkOutDate: '2024-02-18',
    nights: 3,
    adults: 2,
    children: 0,
    status: 'confirmed',
    totalAmount: 750.00,
    currency: 'USD',
    paymentStatus: 'paid',
    bookingSource: 'direct',
    specialRequests: 'Late check-in requested',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z'
  },
  {
    id: '2',
    bookingReference: 'BK2024002',
    hotelId: '2',
    hotelName: 'Seaside Business Hotel',
    roomId: '2',
    roomName: 'Superior City View',
    guestName: 'Sarah Johnson',
    guestEmail: '<EMAIL>',
    guestPhone: '+65 9123 4567',
    checkInDate: '2024-02-20',
    checkOutDate: '2024-02-22',
    nights: 2,
    adults: 1,
    children: 0,
    status: 'confirmed',
    totalAmount: 360.00,
    currency: 'USD',
    paymentStatus: 'pending',
    bookingSource: 'booking.com',
    createdAt: '2024-01-16T14:20:00Z',
    updatedAt: '2024-01-16T14:20:00Z'
  },
  {
    id: '3',
    bookingReference: 'BK2024003',
    hotelId: '1',
    hotelName: 'Grand Palace Resort & Spa',
    roomId: '3',
    roomName: 'Family Suite',
    guestName: 'Michael Brown',
    guestEmail: '<EMAIL>',
    guestPhone: '+44 20 7123 4567',
    checkInDate: '2024-02-25',
    checkOutDate: '2024-02-28',
    nights: 3,
    adults: 2,
    children: 2,
    status: 'cancelled',
    totalAmount: 1050.00,
    currency: 'USD',
    paymentStatus: 'refunded',
    bookingSource: 'expedia',
    cancellationReason: 'Change of travel plans',
    cancelledAt: '2024-01-18T09:15:00Z',
    createdAt: '2024-01-17T16:45:00Z',
    updatedAt: '2024-01-18T09:15:00Z'
  }
];

// Mock data for component booking format
const getMockComponentBookings = (): ComponentBooking[] => [
  {
    id: '1',
    bookingId: 'BK2024001',
    guestName: 'John Smith',
    guestEmail: '<EMAIL>',
    guestPhone: '****** 123 4567',
    hotelId: '1',
    hotelName: 'Grand Palace Resort & Spa',
    roomId: '1',
    roomName: 'Deluxe Ocean View',
    checkInDate: '2024-02-15',
    checkOutDate: '2024-02-18',
    numberOfGuests: 2,
    numberOfNights: 3,
    bookingStatus: 'confirmed',
    paymentStatus: 'paid',
    paymentMethod: 'credit-card',
    totalAmount: 750.00,
    amountPaid: 750.00,
    amountDue: 0,
    currency: 'USD',
    bookingDate: '2024-01-15T10:30:00Z',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    bookingSource: 'direct',
    specialRequests: 'Late check-in requested',
    guestDetails: {
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '****** 123 4567'
    }
  },
  {
    id: '2',
    bookingId: 'BK2024002',
    guestName: 'Sarah Johnson',
    guestEmail: '<EMAIL>',
    guestPhone: '+65 9123 4567',
    hotelId: '2',
    hotelName: 'Seaside Business Hotel',
    roomId: '2',
    roomName: 'Superior City View',
    checkInDate: '2024-02-20',
    checkOutDate: '2024-02-22',
    numberOfGuests: 1,
    numberOfNights: 2,
    bookingStatus: 'confirmed',
    paymentStatus: 'pending',
    paymentMethod: 'credit-card',
    totalAmount: 360.00,
    amountPaid: 0,
    amountDue: 360.00,
    currency: 'USD',
    bookingDate: '2024-01-16T14:20:00Z',
    createdAt: '2024-01-16T14:20:00Z',
    updatedAt: '2024-01-16T14:20:00Z',
    bookingSource: 'booking.com',
    guestDetails: {
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+65 9123 4567'
    }
  }
];
