'use client';

import { useState, useEffect } from 'react';

interface TranslationEditorProps {
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
  readOnly?: boolean;
}

interface TranslationItem {
  key: string;
  value: string;
  path: string[];
}

export default function TranslationEditor({ value, onChange, readOnly = false }: TranslationEditorProps) {
  const [translations, setTranslations] = useState<TranslationItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set());

  // Flatten nested JSON structure for easier editing
  const flattenTranslations = (obj: Record<string, any>, path: string[] = []): TranslationItem[] => {
    const items: TranslationItem[] = [];
    
    Object.entries(obj).forEach(([key, val]) => {
      const currentPath = [...path, key];
      
      if (typeof val === 'object' && val !== null && !Array.isArray(val)) {
        // Nested object - recursively flatten
        items.push(...flattenTranslations(val, currentPath));
      } else {
        // Leaf value - add as translation item
        items.push({
          key,
          value: String(val || ''),
          path: currentPath
        });
      }
    });
    
    return items;
  };

  // Rebuild nested structure from flat array
  const buildNestedStructure = (items: TranslationItem[]): Record<string, any> => {
    const result: Record<string, any> = {};
    
    items.forEach(item => {
      let current = result;
      
      // Navigate to the correct nested position
      for (let i = 0; i < item.path.length - 1; i++) {
        const pathKey = item.path[i];
        if (!current[pathKey]) {
          current[pathKey] = {};
        }
        current = current[pathKey];
      }
      
      // Set the final value
      const finalKey = item.path[item.path.length - 1];
      current[finalKey] = item.value;
    });
    
    return result;
  };

  // Initialize translations from value
  useEffect(() => {
    if (value && typeof value === 'object') {
      const flattened = flattenTranslations(value);
      setTranslations(flattened);
      
      // Auto-expand all sections initially
      const sections = new Set<string>();
      flattened.forEach(item => {
        if (item.path.length > 1) {
          sections.add(item.path[0]);
        }
      });
      setExpandedSections(sections);
    }
  }, [value]);

  // Handle translation value change
  const handleTranslationChange = (index: number, newValue: string) => {
    const updatedTranslations = [...translations];
    updatedTranslations[index].value = newValue;
    setTranslations(updatedTranslations);
    
    // Rebuild and emit the nested structure
    const nestedStructure = buildNestedStructure(updatedTranslations);
    onChange(nestedStructure);
  };

  // Filter translations based on search term
  const filteredTranslations = translations.filter(item => {
    const searchLower = searchTerm.toLowerCase();
    return (
      item.key.toLowerCase().includes(searchLower) ||
      item.value.toLowerCase().includes(searchLower) ||
      item.path.join('.').toLowerCase().includes(searchLower)
    );
  });

  // Group translations by section
  const groupedTranslations = filteredTranslations.reduce((groups, item) => {
    const section = item.path[0];
    if (!groups[section]) {
      groups[section] = [];
    }
    groups[section].push(item);
    return groups;
  }, {} as Record<string, TranslationItem[]>);

  // Toggle section expansion
  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Get display path for a translation item
  const getDisplayPath = (item: TranslationItem) => {
    return item.path.join(' → ');
  };

  return (
    <div className="space-y-4">
      {/* Search and Controls */}
      <div className="flex items-center justify-between">
        <div className="flex-1 max-w-md">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search translations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => setExpandedSections(new Set(Object.keys(groupedTranslations)))}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
          >
            Expand All
          </button>
          <button
            type="button"
            onClick={() => setExpandedSections(new Set())}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded hover:bg-gray-50"
          >
            Collapse All
          </button>
        </div>
      </div>

      {/* Translation Sections */}
      <div className="space-y-4">
        {Object.entries(groupedTranslations).map(([section, items]) => (
          <div key={section} className="border border-gray-200 rounded-lg overflow-hidden">
            {/* Section Header */}
            <button
              type="button"
              onClick={() => toggleSection(section)}
              className="w-full px-4 py-3 bg-gray-50 hover:bg-gray-100 flex items-center justify-between text-left"
            >
              <div className="flex items-center space-x-2">
                <i className={`ri-arrow-${expandedSections.has(section) ? 'down' : 'right'}-s-line text-gray-500`}></i>
                <span className="font-medium text-gray-900 capitalize">{section}</span>
                <span className="text-sm text-gray-500">({items.length} translations)</span>
              </div>
            </button>

            {/* Section Content */}
            {expandedSections.has(section) && (
              <div className="divide-y divide-gray-100">
                {items.map((item, index) => {
                  const globalIndex = translations.findIndex(t => 
                    t.path.join('.') === item.path.join('.')
                  );
                  
                  return (
                    <div key={item.path.join('.')} className="p-4 hover:bg-gray-50">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {/* Key Path */}
                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-gray-700">
                            Translation Key
                          </label>
                          <div className="flex items-center space-x-2">
                            <code className="flex-1 px-3 py-2 bg-gray-100 border border-gray-200 rounded text-sm font-mono text-gray-800">
                              {getDisplayPath(item)}
                            </code>
                            <button
                              type="button"
                              onClick={() => navigator.clipboard.writeText(item.path.join('.'))}
                              className="p-2 text-gray-400 hover:text-gray-600 rounded hover:bg-gray-100"
                              title="Copy key path"
                            >
                              <i className="ri-file-copy-line text-sm"></i>
                            </button>
                          </div>
                        </div>

                        {/* Translation Value */}
                        <div className="space-y-1">
                          <label className="block text-sm font-medium text-gray-700">
                            Translation Value
                          </label>
                          <input
                            type="text"
                            value={item.value}
                            onChange={(e) => handleTranslationChange(globalIndex, e.target.value)}
                            readOnly={readOnly}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                              readOnly 
                                ? 'bg-gray-50 border-gray-200 text-gray-600' 
                                : 'border-gray-300'
                            }`}
                            placeholder="Enter translation..."
                          />
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Empty State */}
      {Object.keys(groupedTranslations).length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <i className="ri-translate-line text-4xl mb-4 block"></i>
          <p className="text-lg font-medium">No translations found</p>
          <p className="text-sm">
            {searchTerm ? 'Try adjusting your search terms' : 'Add some translation data to get started'}
          </p>
        </div>
      )}

      {/* Summary */}
      {translations.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-blue-800">
            <i className="ri-information-line"></i>
            <span className="text-sm font-medium">
              Total: {translations.length} translations across {Object.keys(groupedTranslations).length} sections
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
