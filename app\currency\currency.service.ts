import { Currency, CurrencyFormData, CurrencyResponse, CurrencyFilters } from './currency.model';

// Mock data based on your API response structure
const generateMockCurrencies = (): Currency[] => {
  const mockData = [
    {
      id: 1,
      amount: 1,
      created_at: "2025-09-17T10:08:33.565658Z",
      currency_name: "Emirati <PERSON>",
      currency_symbol: "د.إ",
      currency_symbol_on_right: false,
      from_currency_code: "BHD",
      is_disabled_conversion: false,
      is_disabled_currency: false,
      rate: 9.767287234,
      timestamp: "2025-09-18T21:00:00Z",
      to_currency_code: "AED"
    },
    {
      id: 2,
      amount: 1,
      created_at: "2025-09-17T10:08:33.565658Z",
      currency_name: "US Dollar",
      currency_symbol: "$",
      currency_symbol_on_right: false,
      from_currency_code: "BHD",
      is_disabled_conversion: false,
      is_disabled_currency: false,
      rate: 2.653,
      timestamp: "2025-09-18T21:00:00Z",
      to_currency_code: "USD"
    },
    {
      id: 3,
      amount: 1,
      created_at: "2025-09-17T10:08:33.565658Z",
      currency_name: "Euro",
      currency_symbol: "€",
      currency_symbol_on_right: false,
      from_currency_code: "BHD",
      is_disabled_conversion: false,
      is_disabled_currency: false,
      rate: 2.421,
      timestamp: "2025-09-18T21:00:00Z",
      to_currency_code: "EUR"
    },
    {
      id: 4,
      amount: 1,
      created_at: "2025-09-17T10:08:33.565658Z",
      currency_name: "British Pound",
      currency_symbol: "£",
      currency_symbol_on_right: false,
      from_currency_code: "BHD",
      is_disabled_conversion: false,
      is_disabled_currency: false,
      rate: 2.089,
      timestamp: "2025-09-18T21:00:00Z",
      to_currency_code: "GBP"
    },
    {
      id: 5,
      amount: 1,
      created_at: "2025-09-17T10:08:33.565658Z",
      currency_name: "Japanese Yen",
      currency_symbol: "¥",
      currency_symbol_on_right: false,
      from_currency_code: "BHD",
      is_disabled_conversion: false,
      is_disabled_currency: true,
      rate: 394.25,
      timestamp: "2025-09-18T21:00:00Z",
      to_currency_code: "JPY"
    }
  ];
  return mockData;
};

let mockCurrencies: Currency[] = generateMockCurrencies();

// Currency Service API functions
const CurrencyService = {
  // Get all currencies with pagination and filters
  getAllCurrencies: async (
    page: number = 1,
    pageSize: number = 10,
    filters?: CurrencyFilters
  ): Promise<CurrencyResponse> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    let filteredCurrencies = [...mockCurrencies];

    // Apply filters
    if (filters) {
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredCurrencies = filteredCurrencies.filter(currency =>
          currency.currency_name.toLowerCase().includes(searchLower) ||
          currency.to_currency_code.toLowerCase().includes(searchLower) ||
          currency.currency_symbol.toLowerCase().includes(searchLower)
        );
      }

      if (filters.is_disabled_currency !== undefined) {
        filteredCurrencies = filteredCurrencies.filter(currency =>
          currency.is_disabled_currency === filters.is_disabled_currency
        );
      }

      if (filters.is_disabled_conversion !== undefined) {
        filteredCurrencies = filteredCurrencies.filter(currency =>
          currency.is_disabled_conversion === filters.is_disabled_conversion
        );
      }

      if (filters.currency_code_filter) {
        filteredCurrencies = filteredCurrencies.filter(currency =>
          currency.to_currency_code.toLowerCase().includes(filters.currency_code_filter.toLowerCase())
        );
      }
    }

    // Sort by currency code
    filteredCurrencies.sort((a, b) => a.to_currency_code.localeCompare(b.to_currency_code));

    // Pagination
    const totalItems = filteredCurrencies.length;
    const totalPages = Math.ceil(totalItems / pageSize);
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredCurrencies.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      pagination: {
        current_page: page,
        page_size: pageSize,
        total_items: totalItems,
        total_pages: totalPages,
        has_next: page < totalPages,
        has_previous: page > 1
      },
      success: true,
      message: 'Currencies retrieved successfully'
    };
  },

  // Get currency by ID
  getCurrencyById: async (id: number): Promise<Currency | null> => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockCurrencies.find(currency => currency.id === id) || null;
  },

  // Update currency (only editable fields, rate is not editable)
  updateCurrency: async (id: number, currencyData: Partial<CurrencyFormData>): Promise<Currency> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const currencyIndex = mockCurrencies.findIndex(currency => currency.id === id);
    if (currencyIndex === -1) {
      throw new Error('Currency not found');
    }

    const updatedCurrency: Currency = {
      ...mockCurrencies[currencyIndex],
      ...currencyData,
      // Keep the original rate and other non-editable fields
      rate: mockCurrencies[currencyIndex].rate,
      amount: mockCurrencies[currencyIndex].amount,
      timestamp: new Date().toISOString()
    };

    mockCurrencies[currencyIndex] = updatedCurrency;
    return updatedCurrency;
  },

  // Get currency statistics
  getCurrencyStats: async () => {
    await new Promise(resolve => setTimeout(resolve, 200));

    const totalCurrencies = mockCurrencies.length;
    const activeCurrencies = mockCurrencies.filter(c => !c.is_disabled_currency).length;
    const disabledCurrencies = totalCurrencies - activeCurrencies;
    const lastUpdated = mockCurrencies.reduce((latest, currency) => {
      return new Date(currency.timestamp) > new Date(latest) ? currency.timestamp : latest;
    }, mockCurrencies[0]?.timestamp || new Date().toISOString());

    return {
      totalCurrencies,
      activeCurrencies,
      disabledCurrencies,
      lastUpdated
    };
  },

  // Toggle currency status
  toggleCurrencyStatus: async (id: number): Promise<Currency> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    const currencyIndex = mockCurrencies.findIndex(currency => currency.id === id);
    if (currencyIndex === -1) {
      throw new Error('Currency not found');
    }

    const currency = mockCurrencies[currencyIndex];
    const updatedCurrency: Currency = {
      ...currency,
      is_disabled_currency: !currency.is_disabled_currency,
      timestamp: new Date().toISOString()
    };

    mockCurrencies[currencyIndex] = updatedCurrency;
    return updatedCurrency;
  }
};

export default CurrencyService;
