'use client';

import { useState } from 'react';
import LanguageMaster from './components/LanguageMaster';

export default function LanguagePage() {
  const [lastSyncTime] = useState(new Date().toLocaleTimeString());

  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Professional Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <i className="ri-translate-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Language Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Manage system languages, localization settings, and regional configurations
                  </p>
                </div>
              </div>
            </div>

            {/* Status Cards */}
            <div className="flex flex-col space-y-3 lg:flex-row lg:space-y-0 lg:space-x-6">
              {/* System Status */}
              <div className="bg-green-50 border border-green-200 rounded-lg px-4 py-3">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800">System Status</p>
                    <p className="text-xs text-green-600">All services operational</p>
                  </div>
                </div>
              </div>

              {/* Last Sync */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-3">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-refresh-line text-blue-600 text-sm"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-blue-800">Last Sync</p>
                    <p className="text-xs text-blue-600">{lastSyncTime}</p>
                  </div>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="bg-purple-50 border border-purple-200 rounded-lg px-4 py-3">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <i className="ri-global-line text-purple-600 text-sm"></i>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-purple-800">Localization</p>
                    <p className="text-xs text-purple-600">Multi-language support</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Action Buttons */}
          <div className="mt-6 flex flex-wrap gap-3">
            <button className="inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-purple-500">
              <i className="ri-download-line mr-2"></i>
              Export Languages
            </button>
            <button className="inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-purple-500">
              <i className="ri-upload-line mr-2"></i>
              Import Languages
            </button>
            <button className="inline-flex items-center px-3 py-2 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-purple-500">
              <i className="ri-settings-3-line mr-2"></i>
              Localization Settings
            </button>
          </div>

          {/* Key Metrics */}
          <div className="mt-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">Total Languages</p>
                  <p className="text-2xl font-bold">--</p>
                </div>
                <div className="w-8 h-8 bg-blue-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <i className="ri-translate-line text-lg"></i>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">Active Languages</p>
                  <p className="text-2xl font-bold">--</p>
                </div>
                <div className="w-8 h-8 bg-green-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <i className="ri-check-line text-lg"></i>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">Regions Covered</p>
                  <p className="text-2xl font-bold">--</p>
                </div>
                <div className="w-8 h-8 bg-purple-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <i className="ri-global-line text-lg"></i>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">Countries</p>
                  <p className="text-2xl font-bold">--</p>
                </div>
                <div className="w-8 h-8 bg-orange-400 bg-opacity-30 rounded-lg flex items-center justify-center">
                  <i className="ri-map-pin-line text-lg"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area with Internal Scrolling */}
      <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
        <div className="p-6">
          <div className="max-w-full">
            <div className="animate-fade-in">
              <LanguageMaster />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
